import pako from 'pako';

// Funzione per convertire un Uint8Array in una stringa Base64
function uint8ArrayToBase64(uint8Array) {
    let binaryString = '';
    for (let i = 0; i < uint8Array.length; i++) {
        binaryString += String.fromCharCode(uint8Array[i]);
    }
    return btoa(binaryString);
}

// Funzione per convertire una stringa Base64 in un Uint8Array
function base64ToUint8Array(base64String) {
    const binaryString = atob(base64String);
    const len = binaryString.length;
    const uint8Array = new Uint8Array(len);
    for (let i = 0; i < len; i++) {
        uint8Array[i] = binaryString.charCodeAt(i);
    }
    return uint8Array;
}

export function useZlib() {

    const compress = (json) => {
        try {
            const stringifiedJson = JSON.stringify(json);
            const compressed = pako.deflate(stringifiedJson);
            return uint8ArrayToBase64(compressed); // Converti in base64 il buffer binario
        } catch (error) {
            console.error('Compressione fallita:', error);
            return null;
        }
    };

    const decompress = (compressedBase64) => {
        try {
            const compressed = base64ToUint8Array(compressedBase64); // Converti da base64 a Uint8Array
            const decompressed = pako.inflate(compressed, { to: 'string' });
            return JSON.parse(decompressed); // Restituisci l'oggetto JSON decompresso
        } catch (error) {
            console.error('Decompressione fallita:', error);
            return null;
        }
    };

    return {
        compress,
        decompress
    };
}
