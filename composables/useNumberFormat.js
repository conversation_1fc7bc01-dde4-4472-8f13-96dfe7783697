import { ref } from 'vue';

// Formatter come singleton per Euro
const euroFormatter = new Intl.NumberFormat('de-DE', {
    style: 'currency',
    currency: 'EUR'
});

// Formatter come singleton per Dollaro USA
const usdFormatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
});

// Formatter come singleton per Yen Giapponese
const jpyFormatter = new Intl.NumberFormat('ja-JP', {
    style: 'currency',
    currency: 'JPY'
});

// Funzioni di formattazione esposte
const formatEuro = value => euroFormatter.format(value);

export default function useNumberFormatter() {
    return {
        formatEuro
    };
}
