// composables/useSimpleBookingDownload.js
import { ref } from 'vue';
import { useFetch } from '#app'; // Import di useFetch da Nuxt 3

export function useSimpleBookingDownload(parametri) {
    const isDebugMode = ref(true); // Attiva il debug se necessario
    const prenotazioniTotali = ref([]);

    // Funzione per eseguire il download
    async function scaricaPrenotazioni() {
        const periods = generatePeriods();

        for (const period of periods) {
            const requestXml = createSimpleBookingRequestXml(parametri, period);
            
            try {
                const { data, error } = await useFetch(parametri.find(p => p.name === 'url').value, {
                    method: 'POST',
                    body: requestXml,
                    headers: { 'Content-Type': 'application/xml' }
                });

                if (error.value) throw error.value;

                if (isDebugMode.value) console.log("Risultato della chiamata per periodo", period, data.value);

                prenotazioniTotali.value.push(...data.value); // Assumendo che `data` contenga le prenotazioni
            } catch (error) {
                console.error("Errore nella chiamata Simple Booking per periodo", period, error);
                break;
            }
        }
        return prenotazioniTotali.value;
    }

    function createSimpleBookingRequestXml(parametri, period) {
        const providerName = parametri.find(p => p.name === 'providerName').value;
        const providerPassword = parametri.find(p => p.name === 'providerPassword').value;
        const xmlHotelAgentName = parametri.find(p => p.name === 'xmlHotelAgentName').value;
        const xmlHotelAgentPassword = parametri.find(p => p.name === 'xmlHotelAgentPassword').value;
        const hotelCode = parametri.find(p => p.name === 'hotelCode').value;

        const year = period.a;
        const month = period.m;
        const lastDay = period.d;

        return `xml=<?xml version="1.0" encoding="utf-8"?>
      <OTA_HotelResNotifRQ xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.opentravel.org/OTA/2003/05" xsi:schemaLocation="http://www.opentravel.org/OTA/2003/05 OTA_HotelResNotifRQ.xsd" TimeStamp="${new Date().toISOString()}" Target="Production" Version="2.001">
        <TPA_Extensions>
          <provider Name="${providerName}" Pwd="${providerPassword}"></provider>
          <XMLHotelAgent Name="${xmlHotelAgentName}" Pwd="${xmlHotelAgentPassword}"></XMLHotelAgent>
          <Filter HotelCode="${hotelCode}" Start="${year}-${month}-01" End="${year}-${month}-${lastDay}" ForceSend="1" FilterType="all"></Filter>
        </TPA_Extensions>
      </OTA_HotelResNotifRQ>`;
    }

    function generatePeriods() {
        const periods = [];
        const now = new Date();
        const currentYear = now.getFullYear();
        const currentMonth = now.getMonth() + 1; // Mese corrente (da 1 a 12)
        const startYear = currentYear - 1;

        const isLeapYear = (year) => {
            return (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);
        };

        const daysInMonth = (month, year) => {
            if (month === 2) {
                return isLeapYear(year) ? 29 : 28;
            }
            return [4, 6, 9, 11].includes(month) ? 30 : 31;
        };

        for (let month = 7; month <= 12; month++) {
            periods.push({ d: daysInMonth(month, startYear), m: month.toString().padStart(2, '0'), a: startYear });
        }

        for (let month = 1; month <= currentMonth; month++) {
            periods.push({ d: daysInMonth(month, currentYear), m: month.toString().padStart(2, '0'), a: currentYear });
        }

        return periods;
    }

    return scaricaPrenotazioni();
}
