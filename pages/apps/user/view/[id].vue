<script setup>
import UserBioPanel from '@/views/apps/user/view/UserBioPanel.vue'

const route = useRoute('apps-user-view-id')
const userTab = ref(null)

const tabs = [
  {
    icon: 'ri-group-line',
    title: 'Overview',
  },
  {
    icon: 'ri-lock-2-line',
    title: 'Security',
  },
  {
    icon: 'ri-bookmark-line',
    title: 'Billing & Plan',
  },
  {
    icon: 'ri-notification-4-line',
    title: 'Notifications',
  },
  {
    icon: 'ri-link-m',
    title: 'Connections',
  },
]

const { data: session } = await useApi(`/auth/session`)
const userData = session.value.user
</script>

<template>
  <VRow v-if="userData">
    <VCol
      cols="12"
    >
      <UserBioPanel :user-data="userData" />
    </VCol>

  </VRow>
  <VCard v-else>
    <VCardTitle class="text-center">
      No User Found
    </VCardTitle>
  </VCard>
</template>
