<script setup>
import AddNewStructureDrawer from '@/views/apps/hotel/AddNewStructureDrawer.vue'
import { ref, computed, watch } from 'vue'
import { useTranslateUtil } from '@/utils/translate-util'
import { useI18n } from 'vue-i18n'

definePageMeta({ middleware: 'admin' })

// 👉 Store
const searchQuery = ref('')
const selectedLocation = ref()
const selectedStatus = ref()

// Data table options
const itemsPerPage = ref(10)
const page = ref(1)
const sortBy = ref()
const orderBy = ref()

const updateOptions = options => {
  page.value = options.page
  sortBy.value = options.sortBy[0]?.key
  orderBy.value = options.sortBy[0]?.order
}

// Headers
const { t, tc } = useTranslateUtil()
const headers = ref([])

const updateHeaders = () => {
  headers.value = [
    { title: t('ID'), key: 'id' },
    { title: t('Business Name'), key: 'businessName' },
    { title: t('Location'), key: 'location' },
    { title: t('VAT Number'), key: 'vatNumber' },
    { title: t('Address'), key: 'address' },
    { title: t('City'), key: 'city' },
    { title: t('ZIP Code'), key: 'zipCode' },
    { title: t('Province'), key: 'province' },
    { title: t('Owner Name'), key: 'ownerName' },
    { title: t('Status'), key: 'status' },
    { title: t('Actions'), key: 'actions', sortable: false },
  ]
}

// Aggiornare i titoli delle colonne inizialmente e ogni volta che cambia la lingua
updateHeaders()
watch(() => t('ID'), updateHeaders)

const {
  data: structuresData,
  execute: fetchStructures,
} = await useApi(createUrl('/apps/structures', {
  query: {
    q: searchQuery,
    status: selectedStatus,
    location: selectedLocation,
    itemsPerPage,
    page,
    sortBy,
    orderBy,
  },
}))

const structures = computed(() => structuresData.value.structures)
const totalStructures = computed(() => structuresData.value.totalStructures)

// 👉 search filters
const locations = [
  { title: t('Location 1'), value: 'location1' },
  { title: t('Location 2'), value: 'location2' },
  { title: t('Location 3'), value: 'location3' },
  { title: t('Location 4'), value: 'location4' },
]

const status = [
  { title: t('Pending'), value: 'pending' },
  { title: t('Active'), value: 'active' },
  { title: t('Inactive'), value: 'inactive' },
]

const resolveStructureStatusVariant = stat => {
  const statLowerCase = stat.toLowerCase()
  if (statLowerCase === 'pending') return 'warning'
  if (statLowerCase === 'active') return 'success'
  if (statLowerCase === 'inactive') return 'secondary'

  return 'primary'
}

const isAddNewStructureDrawerVisible = ref(false)


const addNewStructure = async structureData => {
  await $api('/apps/structures', { method: 'POST', body: structureData })
  fetchStructures() // refetch structures
}

const deleteStructure = async id => {
  await $api(`/apps/structures/${id}`, { method: 'DELETE' })
  fetchStructures() // refetch structures
}
</script>

<template>
  <section>
    <!-- 👉 Filters and Search -->
    <VCard :title="t('Filters')" class="mb-6">
      <VCardText>
        <VRow>
          <!-- 👉 Select Location -->
          <VCol cols="12" sm="4">
            <VSelect
              v-model="selectedLocation"
              :label="t('Select Location')"
              :placeholder="t('Select Location')"
              :items="locations"
              clearable
              clear-icon="ri-close-line"
            />
          </VCol>
          <!-- 👉 Select Status -->
          <VCol cols="12" sm="4">
            <VSelect
              v-model="selectedStatus"
              :label="t('Select Status')"
              :placeholder="t('Select Status')"
              :items="status"
              clearable
              clear-icon="ri-close-line"
            />
          </VCol>
          <!-- 👉 Search -->
          <VCol cols="12" sm="4">
            <VTextField
              v-model="searchQuery"
              :placeholder="t('Search Structure')"
              density="compact"
              clearable
              clear-icon="ri-close-line"
            />
          </VCol>
        </VRow>
      </VCardText>
    </VCard>

    <!-- 👉 Data Table -->
    <VDataTableServer
      v-model:items-per-page="itemsPerPage"
      v-model:page="page"
      :headers="headers"
      :items="structures"
      show-select
      :items-length="totalStructures"
      item-value="id"
      class="text-no-wrap rounded-0"
      @update:options="updateOptions"
    >
      <!-- Structure Details -->
      <template #item.businessName="{ item }">
        <div class="d-flex flex-column">
          <h6 class="text-h6">{{ item.businessName }}</h6>
          <span class="text-sm text-medium-emphasis">{{ item.vatNumber }}</span>
        </div>
      </template>

      <template #item.location="{ item }">
        <span class="text-capitalize">{{ tc(item.location) }}</span>
      </template>

      <template #item.status="{ item }">
        <VChip
          :color="resolveStructureStatusVariant(item.status)"
          size="small"
          class="text-capitalize"
        >
          {{ tc(item.status) }}
        </VChip>
      </template>

      <!-- Actions -->
      <template #item.actions="{ item }">
        <IconBtn size="small" @click="deleteStructure(item.id)">
          <VIcon icon="ri-delete-bin-7-line" />
        </IconBtn>

        <IconBtn size="small" :to="{ name: 'apps-structure-view-id', params: { id: item.id } }">
          <VIcon icon="ri-eye-line" />
        </IconBtn>

        <IconBtn size="small" color="medium-emphasis">
          <VIcon size="24" icon="ri-more-2-line" />
          <VMenu activator="parent">
            <VList>
              <VListItem link>
                <template #prepend>
                  <VIcon icon="ri-download-line" />
                </template>
                <VListItemTitle>{{ t('Download') }}</VListItemTitle>
              </VListItem>
              <VListItem link>
                <template #prepend>
                  <VIcon icon="ri-edit-box-line" />
                </template>
                <VListItemTitle>{{ t('Edit') }}</VListItemTitle>
              </VListItem>
            </VList>
          </VMenu>
        </IconBtn>
      </template>

      <!-- Pagination -->
      <template #bottom>
        <VDivider />

        <div class="d-flex justify-end flex-wrap gap-x-6 px-2 py-1">
          <div class="d-flex align-center gap-x-2 text-medium-emphasis text-base">
            {{ t('Rows Per Page:') }}
            <VSelect
              v-model="itemsPerPage"
              class="per-page-select"
              variant="plain"
              :items="[10, 20, 25, 50, 100]"
            />
          </div>

          <p class="d-flex align-center text-base text-high-emphasis me-2 mb-0">
            {{ paginationMeta({ page, itemsPerPage }, totalStructures) }}
          </p>

          <div class="d-flex gap-x-2 align-center me-2">
            <VBtn
              class="flip-in-rtl"
              icon="ri-arrow-left-s-line"
              variant="text"
              density="comfortable"
              color="high-emphasis"
              :disabled="page <= 1"
              @click="page <= 1 ? page = 1 : page--"
            />
            <VBtn
              class="flip-in-rtl"
              icon="ri-arrow-right-s-line"
              density="comfortable"
              variant="text"
              color="high-emphasis"
              :disabled="page >= Math.ceil(totalStructures / itemsPerPage)"
              @click="page >= Math.ceil(totalStructures / itemsPerPage) ? page = Math.ceil(totalStructures / itemsPerPage) : page++"
            />
          </div>
        </div>
      </template>
    </VDataTableServer>

    <!-- 👉 Add New Structure -->
    <AddNewStructureDrawer
      v-model:isDrawerOpen="isAddNewStructureDrawerVisible"
      @structure-data="addNewStructure"
    />
  </section>
</template>

<style lang="scss">
.text-capitalize {
  text-transform: capitalize;
}
</style>
