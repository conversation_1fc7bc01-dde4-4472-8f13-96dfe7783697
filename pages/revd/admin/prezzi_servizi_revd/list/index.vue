<script setup>
import { ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useAppStore } from '@/stores/app'
const accessToken = useCookie('accessToken')
definePageMeta({ 'action': 'manage', 'subject': 'loggedin' })

// Data table options
const itemsPerPage = ref(10)
const page = ref(1)
const sortBy = ref()
const orderBy = ref()

const updateOptions = options => {
  page.value = options.page
  sortBy.value = options.sortBy[0]?.key
  orderBy.value = options.sortBy[0]?.order
}

// Headers
const { t } = useI18n()
const headers = ref([
  { title: t('Descrizione'), key: 'description', width: '80%' },
  { title: t('Prezzo (€)'), key: 'price'},
  { title: t('Azioni'), key: 'actions', sortable: false },
])

const allRecords = ref([])
const isModified = ref(false)
const loadingSave = ref(false)
const loading = ref(false)
const appStore = useAppStore()

// Record data
const recordData = ref({
  nome_lista: 'servizi-aggiuntivi-revd',
  lista: []
})

const loadRecords = async () => {
  loading.value = true
  allRecords.value = []
  const { data: recordsData } = await useApi(createUrl('/resources', { query: { tb: 'liste', pk: 'nome_lista', sk: 'servizi-aggiuntivi-revd' } }))

  if (recordsData.value.body && recordsData.value.body.lista && recordsData.value.body.lista.length > 0) {
    recordData.value = recordsData.value.body
    allRecords.value = [...recordData.value.lista]
  } else {
    // Se non ci sono record, preparare una struttura vuota
    recordData.value = {
      nome_lista: 'servizi-aggiuntivi-revd',
      lista: []
    }
  }

  isModified.value = false
  loading.value = false
}

const markAsModified = () => {
  isModified.value = true
}

const saveRecord = async () => {
  loadingSave.value = true
  recordData.value.lista = [...allRecords.value]

  await $api(`/resources?tb=liste`, {
    method: 'POST',
    body: recordData.value
  });

  isModified.value = false
  loadingSave.value = false
}

const deleteRecord = async description => {
  const confirmed = confirm(`Elimino "${description}"?`)
  if (confirmed) {
    allRecords.value = allRecords.value.filter(record => record.description !== description)
    markAsModified()
  }
}

const addNewRecord = () => {
  const newRecord = {
    description: '',
    price: 0
  };
  allRecords.value.push(newRecord)
  markAsModified()
}

await loadRecords()

</script>

<template>
  <section>
    <v-skeleton-loader boilerplate type="table" v-if="loading"></v-skeleton-loader>
    <VCard v-if="!loading">
      <VDivider />
      <VCardText class="d-flex flex-wrap gap-4">
        <VCardTitle>{{ t('Servizi Aggiuntivi RevD') }}</VCardTitle>
        <VSpacer />
        <div class="app-user-buttons d-flex justify-end">
          <VBtn
            v-if="$can('edit', 'admin-data') && isModified"
            @click="saveRecord"
            :loading="loadingSave"
            color="white"
            icon="ri-save-line"
            size="x-large"/>
          <VBtn
            v-if="$can('edit', 'admin-data')"
            @click="addNewRecord"
            class="ml-2"
            icon="ri-file-add-line"
            color="white"
            size="x-large"/>
        </div>
      </VCardText>

      <v-alert
        v-if="isModified"
        class="mb-2"
        density="compact"
        :text="t('Ci sono modifiche da salvare')"
        :title="t('Salvataggio')"
        type="error"
      ></v-alert>

      <!-- SECTION datatable -->
      <VDataTable
        :items="allRecords"
        :headers="headers"
        class="text-no-wrap rounded-0"
        density="compact"
      >
        <!-- Description -->
        <template #item.description="{ item }">
          <VTextField
            :flat="!$can('edit', 'admin-data')"
            variant="solo"
            density="compact"
            v-model="item.description"
            :readonly="!$can('edit', 'admin-data')"
            @input="markAsModified"
          />
        </template>

        <!-- Price -->
        <template #item.price="{ item }">
          <VTextField
            :flat="!$can('edit', 'admin-data')"
            variant="solo"
            density="compact"
            v-model="item.price"
            v-maska="'########'"
            :readonly="!$can('edit', 'admin-data')"
            @input="markAsModified"
          />
        </template>

        <!-- Actions -->
        <template #item.actions="{ item }">
          <IconBtn
            size="small"
            @click="deleteRecord(item.description)"
            v-if="$can('edit', 'admin-data')">
            <VIcon icon="ri-delete-bin-7-line" />
          </IconBtn>
        </template>

        <!-- Pagination -->
        <template #bottom>
          <VDivider />
        </template>
      </VDataTable>
    </VCard>
  </section>
</template>

<style lang="scss">
.app-user-buttons {
  inline-size: 24.0625rem;
}

.text-capitalize {
  text-transform: capitalize;
}
</style>
