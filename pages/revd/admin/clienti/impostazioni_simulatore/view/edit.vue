<template>
  <section fluid>
    <v-skeleton-loader
      elevation="12"
      v-if="isLoading"
      boilerplate
      type="table-heading, list-item-three-line, image, table-tfoot"
    ></v-skeleton-loader>
    <VCard v-else class="mx-auto" width="100%">
      <VCardTitle   class="mb-2">
        {{ t('Impostazioni Focus') }}
      </VCardTitle>

      <VCardText>
        <VForm ref="refForm" v-model="isFormValid" @submit.prevent="onSubmit">
          <VRow>
            <!-- Gestione Costi -->
            <VCol cols="12">
              <VSelect
                v-model="form.gestione_costi"
                :items="gestioneCostiOptions"
                :rules="[requiredValidator]"
                :label="t('Gestione Costi')"
                @update:modelValue="onFormChange"
                required
              />
            </VCol>

            <!-- MOL Libero -->
            <VCol cols="12">
              <VTextField
                v-model="form.mol_libero"
                :label="t('MOL Libero - Forzato (%)')"
                @input="onFormChange"
                required
              />
            </VCol>

            <!-- Tipo Eventi -->
            <VCol cols="12">
              <VSelect
                v-model="form.tipo_eventi"
                :items="tipoEventiOptions"
                :rules="[requiredValidator]"
                :label="t('Tipo Eventi')"
                @update:modelValue="onFormChange"
                required
              />
            </VCol>

            <!-- Tipo Prezzi -->
            <VCol cols="12">
              <VSelect
                v-model="form.tipo_prezzi"
                :items="tipoPrezziOptions"
                :rules="[requiredValidator]"
                :label="t('Tipo Prezzi')"
                @update:modelValue="onFormChange"
                required
              />
            </VCol>

            <VCol cols="12">
              <VSelect
                v-model="form.strategia_disponibilita"
                :items="strategieDisponibilita"
                :label="t('Strategia Disponibilità')"
                @update:modelValue="onFormChange"
                required
              />
            </VCol>

            <!-- Submit Button -->
            <VCol cols="12">
              <VBtn
                :loading="btnLoading"
                type="submit"
                :disabled="!formChanged"
                class="me-4"
                color="white"
                icon="ri-save-line"
                size="x-large"
              />
            </VCol>
          </VRow>

          <!-- Alert for Unsaved Changes -->
          <VRow>
            <VCol cols="12">
              <v-alert
                v-if="formChanged"
                class="mb-2"
                density="compact"
                :text="t('Ci sono modifiche da salvare')"
                :title="t('Salvataggio')"
                type="error"
              ></v-alert>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>

    <!--v-snackbar
      timeout="800"
      v-model="snackbar"
      color="success"
      v-if="snackbar"
      transition="scale-transition"
    >
      {{ t('Impostazioni salvate con successo') }}
    </v-snackbar-->
  </section>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { useAppStore } from '@/stores/app';
import { useImpostazioniSimulatore } from '@/stores/useImpostazioniSimulatore';

definePageMeta({ action: 'manage', subject: 'loggedin' });

const { t } = useI18n();
const appStore = useAppStore();
const impostazioniSimulatoreStore = useImpostazioniSimulatore();
const isFormValid = ref(false);
const refForm = ref();
const snackbar = ref(false);
const isLoading = ref(false);
const btnLoading = ref(false);
const formChanged = ref(false);
const accessToken = useCookie('accessToken')
const numeroCliente = computed(() => appStore.numeroCliente)


const strategieDisponibilita = [
  {title: t('Disponibilità automatica'), value: 'automatica'},
  {title: t('Disponibilità annuale'),  value: 'annuale'},
]

const form = ref({
  numero_cliente: '',
  gestione_costi: '',
  mol_libero: '',
  tipo_eventi: '',
  tipo_prezzi: 'Prezzi in RO',
  strategia_disponibilita: '',
});

const gestioneCostiOptions = ['automatica', 'manuale'];
const tipoEventiOptions = ['Eventi Distribuiti', 'Eventi Unici'];
const tipoPrezziOptions = ['Prezzi in RO'];

const requiredValidator = (value) => !!value || t('Campo obbligatorio');

// Carica i dati quando il componente viene montato
onMounted(() => {
  fetchData(appStore.numeroCliente);
});
watch(() => accessToken.value, (newAccessToken) => {
  fetchData(appStore.numeroCliente)
})

// Carica i dati delle impostazioni
const fetchData = async (numeroCliente) => {
  if (!numeroCliente) return;
  isLoading.value = true;

  try {
    await impostazioniSimulatoreStore.loadImpostazioniSimulatore(numeroCliente);

    const data = impostazioniSimulatoreStore.getImpostazioniSimulatore;
    if (data) {
      form.value = {
        numero_cliente: data.numero_cliente || numeroCliente,
        gestione_costi: data.gestione_costi || '',
        mol_libero: data.mol_libero || '',
        tipo_eventi: data.tipo_eventi || '',
        tipo_prezzi: data.tipo_prezzi || 'Prezzi in RO',
        strategia_disponibilita: data.strategia_disponibilita || '',
      };
      formChanged.value = false; // Reset dopo il caricamento
    }
  } catch (error) {
    console.error("Errore durante il caricamento delle impostazioni:", error);
  } finally {
    isLoading.value = false;
  }
};

// Rileva modifiche nel form
const onFormChange = () => {
  formChanged.value = true;
};

// Gestione dell'invio del form
const onSubmit = async () => {
  refForm.value?.validate().then(async ({valid}) => {
    if (valid) {
      btnLoading.value = true;
      try {
        await $api('/resources?tb=impostazioni_simulatore', {method: 'POST', body: form.value});
        snackbar.value = true;
        formChanged.value = false; // Reset dopo il salvataggio
        useFocus().clearFocusCache(numeroCliente.value);
      } catch (error) {
        console.error("Errore durante il salvataggio:", error);
      } finally {

        btnLoading.value = false;
      }
    } else {
      alert(t('Compilare tutti i campi obbligatori'));
    }
  });
};

// Funzione per resettare il form
const resetForm = () => {
  refForm.value?.reset();
  formChanged.value = false; // Reset dopo il reset manuale
};
</script>
