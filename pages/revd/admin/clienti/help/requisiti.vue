<script setup lang="ts">
definePageMeta({ action: 'manage', subject: 'loggedin' });
</script>

<template>

  <v-sheet :elevation="3"  border class="pa-4">
    <h3 >Assistenza e Manutenzione del Software con Chrome Remote Desktop</h3>
    <pre>

    Per gestire in modo efficace l'assistenza e la manutenzione del software, utilizziamo Chrome Remote Desktop,
    una piattaforma gratuita e sicura che consente interventi rapidi direttamente sul vostro computer,
    riducendo i tempi di inattività.

    <b>Vantaggi di Chrome Remote Desktop</b>
    Connessione remota sicura e trasparente.
    Diagnosi e interventi tecnici senza necessità di visite in loco.
    Maggiore efficienza e risoluzione immediata dei problemi.

    <b>Configurazione di Chrome Remote Desktop</b>
    Accedi al sito ufficiale
    Apri Google Chrome e visita: https://remotedesktop.google.com.
    Accedi con un account Google (Gmail)
    È indispensabile accedere con un account Google (email Gmail) per configurare e utilizzare Chrome Remote Desktop.
    Se non hai un account Google, puoi crearne uno gratuitamente qui: Crea un account Google.
    Aggiungi l'estensione
    Clicca su Scarica, aggiungi l’estensione dal Chrome Web Store e installala.
    Scarica e installa il software
    Scarica il file di supporto (.msi per Windows, .dmg per Mac) e segui le istruzioni di installazione.
    Configura il tuo computer
    Assegna un nome al dispositivo e imposta un PIN di almeno 6 cifre per l'accesso remoto.

    <b>Come consentire l’accesso da parte dell’Assistenza al proprio schermo</b>
    Una volta configurato Chrome Remote Desktop, si accede e si presenterà una schermata in si dovrà cliccare  +Genera Codice
    Il sistema genererà un codice numerico di 12 cifre.
    Il codice dovrà essere spedito via WhatsApp al numero di Assistenza di QuoProfit +39 **********
    L’assistenza potrà quindi, in remoto, gestire l’attività prevista

    <b>IMPORTANTE</b>
    Tutte le attività di assistenza in remoto devono essere preventivamente programmate attraverso l'apertura di un ticket
    nella sezione Tickets presente nel menu del software QuoProfit.
    </pre>

  </v-sheet>


</template>

<style scoped>

</style>
