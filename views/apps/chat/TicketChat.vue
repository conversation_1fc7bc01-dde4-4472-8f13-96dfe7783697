<template>
  <section>
    <VMain class="chat-content-container">
      <div v-if="store.activeChat" class="d-flex flex-column h-100 mt-6">
        <!-- Chat log -->
        <PerfectScrollbar ref="chatLogPS" tag="ul" class="flex-grow-1"></PerfectScrollbar>
        <ChatLog class="chat-log-container" />

        <!-- Form messaggio -->
        <VForm class="chat-log-message-form mb-5 mx-5" @submit.prevent="sendMessage">
          <div class="d-flex align-end">
            <VTextarea
              v-model="msg"
              variant="solo"
              rounded="16"
              density="compact"
              class="flex-grow-1 chat-message-input"
              :placeholder="t('messaggio')+'...'"
              auto-grow
              rows="1"
              no-resize
              autofocus
              @keydown="handleKeyDown"
            />
            <VBtn
              icon="ri-send-plane-2-line"
              class="ml-4 align-self-end"
              @click="sendMessage"
              color="success"
            />
          </div>
        </VForm>
      </div>
    </VMain>
  </section>
</template>

<script setup>
import { ref, nextTick, onBeforeMount, computed, watch } from 'vue'
import PerfectScrollbar from 'vue3-perfect-scrollbar'
import ChatLog from '@/views/apps/chat/ChatLog.vue'
import { useChatStore } from '@/views/apps/chat/useChatStore'
import { useI18n } from 'vue-i18n'
import { useAppStore } from '@/stores/app'
const appStore = useAppStore()
const numeroCliente = computed(() => appStore.numeroCliente)
const { t } = useI18n()
// Prop per i ticket
const props = defineProps({
  ticket: {
    type: Object,
    required: true,
  },
})

// Emissione di eventi per comunicare col componente padre
const emit = defineEmits(['save-ticket'])

const store = useChatStore()

// Riferimento per l'elemento PerfectScrollbar
const chatLogPS = ref()

// Messaggio
const msg = ref('')

// Scorrere in fondo alla chat
const scrollToBottomInChatLog = () => {
  const scrollEl = chatLogPS.value?.$el || chatLogPS.value
  if (scrollEl) {
    scrollEl.scrollTop = scrollEl.scrollHeight
  }
}

const user = useCookie('userData').value
const contact = computed(() => ({
  id: user.role === 'client' ? numeroCliente.value : 'assistenza',
}))

// Invia messaggio
const sendMessage = async () => {
  if (!msg.value.trim()) return

  // Aggiungi il messaggio alla chat del ticket
  props.ticket.chat.push({
    message: msg.value,
    time: new Date().toISOString(),
    senderId: contact.value.id,
  })

  // Emetti l'evento per salvare il ticket
  emit('save-ticket', props.ticket, 'from-chat')

  // Reset input messaggio
  msg.value = ''

  // Scorrere in fondo
  nextTick(() => {
    scrollToBottomInChatLog()
  })
}

// Gestore degli eventi keydown
const handleKeyDown = (event) => {
  if (event.key === 'Enter') {
    if (!event.shiftKey) {
      event.preventDefault() // Previene l'inserimento della nuova riga
      sendMessage() // Invia il messaggio
    }
  }
}

// Funzione per caricare la chat corrente
const loadCurrentChat = async () => {
  if (props.ticket) {
    // Controlla se esiste una chat nel ticket
    if (!props.ticket.chat || props.ticket.chat.length === 0) {
      // Crea una nuova chat con un messaggio iniziale dal supporto
      props.ticket.chat = [

      ]
      // Emetti l'evento per salvare il ticket
      emit('save-ticket', props.ticket, 'from-chat' )
    }

    await store.getChat(props.ticket)

    nextTick(() => {
      scrollToBottomInChatLog()
    })
  }
}

// Richiama la funzione `loadCurrentChat` quando cambia il ticket
watch(() => props.ticket, loadCurrentChat, { immediate: true })
</script>

<style lang="scss">
.chat-content-container {
  background-color: #f5f5f5;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chat-message-input {
  margin-top: auto;
  padding-bottom: 4px;
}
</style>
