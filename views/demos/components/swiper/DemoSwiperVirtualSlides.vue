<script setup>
import { register } from 'swiper/element/bundle'

register()

const slides = Array.from({ length: 500 }, (_, index) => `Slides ${ index + 1 }`)
const swiperEl = ref(null)
const prependNumber = ref(1)
const appendNumber = ref(500)

const toSlide = index => {
  swiperEl.value?.swiper.slideTo(index - 1)
}

const prependSlide = () => {
  swiperEl.value?.swiper.prependSlide([
    `<swiper-slide>Slide ${ --prependNumber.value } </swiper-slide>`,
    `<swiper-slide>Slide ${ --prependNumber.value } </swiper-slide>`,
  ])
}

const appendSlide = () => {
  swiperEl.value?.swiper.appendSlide([`<swiper-slide>Slide ${ ++appendNumber.value } </swiper-slide>`])
}
</script>

<template>
  <section class="swiper-virtual">
    <ClientOnly>
      <swiper-container
        ref="swiperEl"
        virtual="true"
        :slides="slides"
        navigation="true"
        slides-per-view="5"
        space-between="50"
        free-mode="true"
        events-prefix="swiper-"
        :breakpoints="{
          1024: {
            slidesPerView: 4,
            spaceBetween: 40,
          },
          768: {
            slidesPerView: 3,
            spaceBetween: 30,
          },
          640: {
            slidesPerView: 2,
            spaceBetween: 20,
          },
          320: {
            slidesPerView: 1,
            spaceBetween: 10,
          },
        }"
      >
        <swiper-slide
          v-for="(item, index) in slides"
          :key="index"
        >
          {{ item }}
        </swiper-slide>
      </swiper-container>
    </ClientOnly>

    <div class="d-flex justify-center gap-4 flex-wrap">
      <VBtn
        variant="outlined"
        color="primary"
        @click.prevent="prependSlide"
      >
        Prepend 2 Slides
      </VBtn>
      <VBtn
        variant="outlined"
        color="primary"
        @click.prevent="toSlide(1)"
      >
        Slide 1
      </VBtn>
      <VBtn
        variant="outlined"
        color="primary"
        @click.prevent="toSlide(250)"
      >
        Slide 250
      </VBtn>
      <VBtn
        variant="outlined"
        color="primary"
        @click.prevent="toSlide(500)"
      >
        Slide 500
      </VBtn>
      <VBtn
        variant="outlined"
        color="primary"
        @click.prevent="appendSlide"
      >
        Append Slide
      </VBtn>
    </div>
  </section>
</template>

<style lang="scss">
.swiper-virtual {
  swiper-slide {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #eee;
    font-size: 18px;
    text-align: center;
  }

  swiper-container {
    block-size: 300px;
    inline-size: 100%;
    margin-block: 20px;
    margin-inline: auto;
  }
}
</style>
