<script setup lang="ts">
import { useCurrencyInput } from 'vue-currency-input';
import { watch } from 'vue';

const props = defineProps({
  modelValue: {
    type: Number,
    required: true
  },
  currency: {
    type: String,
    default: 'EUR'
  },
  precision: {
    type: Number,
    default: 2
  }
});

const { inputRef, formattedValue, numberValue, setValue } = useCurrencyInput({
  currency: props.currency,
  hideCurrencySymbolOnFocus: false,
  hideGroupingSeparatorOnFocus: false,
  currencyDisplay: 'hidden',
  precision: props.precision
});

watch(
  () => props.modelValue,
  (value) => {
    setValue(value);
  }
);
</script>

<template>
  <VTextField
    v-model="formattedValue"
    density="compact"
    ref="inputRef"
  >
  </VTextField>
</template>
