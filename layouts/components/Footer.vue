<template>
  <div class="h-100 d-flex align-center justify-space-between text-medium-emphasis">
    <!-- 👉 Footer: left content -->
    <span class="d-flex align-center">
      &copy; 2025 QuoProfit

    </span>

    <span class="d-flex align-center mr-10" >
      <VBtn  class="mr-1" icon="ri-grid-line" to="/revd/admin/clienti/simulatore/view/edit" size="large" v-if="showFocusIcon" />
      <VBtn color="success" icon="ri-home-smile-line" to="/" size="large" v-if="route.path!='/'" />

    </span>



  </div>
</template>
<script setup>
const route = useRoute()
const showFocusIcon = computed(() => {
   if(route.path.includes('/mappatura_sigle/list')) return true;
   if(route.path.includes('/impostazioni_simulatore/view/edit')) return true;

   return false
});

</script>
