# Analisi Relazioni useSimulatore.js ↔ edit.vue

## Panoramica Architetturale

Il sistema di simulazione prezzi è basato su un'architettura **Store-View** dove:
- **`useSimulatore.js`** (Store Pinia) gestisce la logica di business e i dati
- **`edit.vue`** (Vista) gestisce l'interfaccia utente e le interazioni

## Flusso di Inizializzazione

### 1. Setup Iniziale (edit.vue → useSimulatore)
```javascript
// edit.vue - watch numeroCliente
watch(numeroCliente, async (newVal) => {
  if (newVal) {
    loading.value = true;
    await simulatoreStore.setup(newVal);  // ← Chiamata principale
    calendario.value = simulatoreStore.calendario;
    loading.value = false;
  }
}, { immediate: true });
```

### 2. Setup Store (useSimulatore.js)
```javascript
async setup(numeroCliente) {
  // Carica tutti gli store dipendenti
  await Promise.all([
    mappaSigleuaStore.loadMappaSigleua(numeroCliente),
    clienteStore.loadCliente(numeroCliente),
    // ... altri store
  ]);
  
  // Calcola prezzi e genera calendario
  this.calculatePrezzoListinoPerNumeroUnita();
  this.calculatePesoSuTipologiaUaAuto();
  await this.generateCalendario();  // ← Metodo da reingegnerizzare
}
```

## Metodi Critici da Reingegnerizzare

### 1. generateCalendario() - PRIORITÀ ALTA
**Posizione**: `useSimulatore.js:197-775`
**Complessità**: ~580 righe di codice
**Responsabilità**:
- Genera struttura calendario completa
- Calcola prezzi per ogni sigla/data
- Applica logiche di pricing complesse
- Gestisce occupazione e eventi

**Problemi Identificati**:
- Metodo monolitico troppo lungo
- Logica di calcolo embedded
- Difficile testing e manutenzione
- Performance issues su calendari grandi

### 2. determinaCostoFocus() - PRIORITÀ ALTA
**Posizione**: `useSimulatore.js:426-554`
**Complessità**: ~130 righe di codice
**Responsabilità**:
- Calcola costo focus per sigla/data
- Gestisce eventi unici vs distribuiti
- Applica pesi e categorie

## Sistema di Modifica Prezzi - Priorità Input

### Gerarchia di Priorità (edit.vue)
```javascript
const handleChangeValue = (key, sigla, param, value) => {
  switch (param) {
    case "tipotariffa":        // PRIORITÀ 1: Tipo tariffa (C/M/R/A/B)
      handleComboChange(key, value);
      break;
    case "modificaperccol":    // PRIORITÀ 2: Modifica % colonna
      break;
    case "garantito":          // PRIORITÀ 3: Prezzo garantito
      saveValue(key, sigla, param, value);
      break;
    case "modificaperc":       // PRIORITÀ 4: Modifica % singola cella
      saveValue(key, sigla, param, value);
      break;
    case "prezzoforzato":      // PRIORITÀ 5: Prezzo forzato (override totale)
      saveValue(key, sigla, param, value);
      break;
  }
  
  // Salvataggio e ricalcolo
  setTimeout(() => {
    simulatoreStore.saveCalendarioToIndexedDB(toRaw(simulatoreStore.calendario));
    changePrezzoByInput(key, sigla, param, value);
  }, 50);
};
```

### Logica di Applicazione Prezzi (useSimulatore.js)
```javascript
const applyPriceModifications = (basePrice, garantito, modificaperc, prezzoforzato) => {
  let finalPrice = basePrice;
  
  // PRIORITÀ 1: Prezzo Forzato (override completo)
  if (prezzoforzato && prezzoforzato > 0) {
    finalPrice = prezzoforzato;
  } else {
    // PRIORITÀ 2: Prezzo Garantito (minimo)
    if (garantito && finalPrice < garantito) {
      finalPrice = garantito;
    }
    // PRIORITÀ 3: Modifica Percentuale
    if (modificaperc && modificaperc !== 0) {
      finalPrice = finalPrice * (1 + modificaperc / 100);
    }
  }
  return finalPrice;
};
```

## Interazioni Store ↔ View

### 1. Modifica Valori (edit.vue → useSimulatore)
```javascript
// edit.vue
const saveValue = (key, sigla, param, newValue) => {
  const day = simulatoreStore.calendario[key];
  const siglaData = day.sigle.find((s) => s.siglaUa === sigla);
  siglaData[param] = newValue;
};

// useSimulatore.js
async saveValue(key, sigla, param, newValue) {
  const day = this.calendario[key];
  const siglaData = day.sigle.find((s) => s.siglaUa === sigla);
  siglaData[param] = newValue * 1; // Conversione numerica
  await this.saveCalendarioToIndexedDB(toRaw(this.calendario));
}
```

### 2. Cambio Tipo Tariffa (edit.vue)
```javascript
const handleComboChange = (key, value) => {
  const valueMap = {
    C: "costo",           // Costo
    M: "prezzomup",       // Prezzo MUP
    R: "prezzorevenue",   // Prezzo Revenue
    A: "concorrenza",     // Concorrenza
    B: "rmc"              // BAR/RMC
  };
  
  // Applica nuovo valore a tutte le sigle della data
  calendarioRowSigle.forEach((sigla) => {
    sigla.prezzo = sigla[valueMap[value]] || "";
  });
};
```

### 3. Rigenerazione Calendario (edit.vue)
```javascript
const reGenerateCalendario = async () => {
  loading.value = true;
  await simulatoreStore.generateCalendario();  // ← Rigenera tutto
  await navigateTo('/revd/admin/clienti/simulatore/view/loader');
  await navigateTo('/revd/admin/clienti/simulatore/view/edit');
};
```

## Persistenza Dati

### IndexedDB Integration
```javascript
// Salvataggio automatico
simulatoreStore.saveCalendarioToIndexedDB(toRaw(simulatoreStore.calendario));

// Caricamento
const calendario = await this.loadCalendarioFromIndexedDB();

// Compressione dati
const { compress } = useZlib();
const compressedData = compress(calendario);
```

## Problemi Architetturali Identificati

### 1. Accoppiamento Forte
- `edit.vue` accede direttamente a `simulatoreStore.calendario`
- Logica di business mista nella vista
- Difficile testing isolato

### 2. Performance Issues
- `generateCalendario()` rigenera tutto il calendario
- Nessuna ottimizzazione per modifiche incrementali
- Salvataggio IndexedDB ad ogni modifica

### 3. Gestione Stato Complessa
- Stato distribuito tra store e componente
- Sincronizzazione manuale required
- Race conditions possibili

## Raccomandazioni per Reingegnerizzazione

### 1. Scomposizione generateCalendario()
```javascript
// Invece di un metodo monolitico
generateCalendario() { /* 580 righe */ }

// Scomporre in:
async generateCalendario() {
  const dateRange = this.calculateDateRange();
  const categories = this.buildCategories();
  
  for (const date of dateRange) {
    await this.generateDateData(date, categories);
  }
}

generateDateData(date, categories) { /* logica per singola data */ }
calculatePricing(sigla, date) { /* logica pricing */ }
applyModifications(basePrice, modifications) { /* applicazione modifiche */ }
```

### 2. Pattern Command per Modifiche
```javascript
class PriceModificationCommand {
  execute(calendario, key, sigla, param, value) {
    // Logica di modifica con undo/redo
  }
}
```

### 3. Reactive State Management
```javascript
// Invece di accesso diretto
calendario.value = simulatoreStore.calendario;

// Usare computed reattivi
const calendario = computed(() => simulatoreStore.calendario);
```

### 4. Ottimizzazione Performance
- Lazy loading per date non visibili
- Debouncing per salvataggi
- Incremental updates invece di rigenerazione completa
- Virtual scrolling per calendari grandi

## Metodi Store da Mantenere

### Funzioni di Calcolo (OK)
- `finalPriceN()` - Calcolo prezzo finale per N persone
- `prezziRevenueN()` - Calcolo revenue per N persone  
- `calculateAdditionalPrice()` - Calcolo prezzo persone aggiuntive
- `applyPriceModifications()` - Applicazione modifiche prezzi

### Funzioni di Persistenza (OK)
- `saveCalendarioToIndexedDB()`
- `loadCalendarioFromIndexedDB()`
- `clearCalendario()`

## Conclusioni

Il sistema attuale funziona ma presenta problemi di:
- **Scalabilità**: `generateCalendario()` troppo complesso
- **Manutenibilità**: Logica dispersa e accoppiata
- **Performance**: Rigenerazione completa ad ogni modifica
- **Testing**: Difficile testare componenti isolati

La reingegnerizzazione dovrebbe focalizzarsi su:
1. Scomposizione metodi monolitici
2. Separazione responsabilità Store/View
3. Ottimizzazione performance
4. Miglioramento architettura reattiva
