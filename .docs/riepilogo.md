# Riepilogo Progetto RevD - Frontend

## Panoramica Generale

**RevD** (Revenue Dynamics) è un'applicazione web avanzata per la gestione e simulazione di prezzi e disponibilità per strutture ricettive. Il progetto è basato su **Nuxt 3** con **Vue 3** e **Vuetify** come framework UI, utilizzando **Pinia** per la gestione dello stato.

## Informazioni Tecniche

### Stack Tecnologico
- **Framework**: Nuxt 3.12.4 (SSR disabilitato, target: static)
- **Frontend**: Vue 3.3.13 + Vuetify 3.4.4
- **State Management**: Pinia 2.1.7
- **Autenticazione**: @sidebase/nuxt-auth con NextAuth
- **Styling**: SCSS + Vuetify
- **Build Tool**: Vite 5.0.10
- **Package Manager**: PNPM 9.1.1

### Architettura del Progetto

```
revd-fe/
├── @core/                    # Core components e utilities
├── @layouts/                 # Layout components
├── assets/                   # Assets statici (immagini, stili)
├── components/               # Componenti Vue riutilizzabili
├── composables/              # Composables Vue
├── layouts/                  # Layout dell'applicazione
├── middleware/               # Middleware di routing
├── navigation/               # Configurazione menu
├── pages/                    # Pagine dell'applicazione
├── plugins/                  # Plugin Nuxt
├── server/                   # API server-side
├── stores/                   # Store Pinia
├── utils/                    # Utilities
└── views/                    # Viste complesse
```

## Funzionalità Principali

### 1. Sistema di Autenticazione e Autorizzazione
- **Ruoli utente**: Admin, Client, Collaborator
- **Middleware di protezione**: ACL (Access Control List)
- **Provider**: Credentials-based authentication
- **Gestione sessioni**: JWT tokens

### 2. Simulatore di Prezzi e Disponibilità
Il cuore dell'applicazione è il **Simulatore** che permette di:
- Calcolare prezzi dinamici basati su occupazione
- Gestire disponibilità per unità abitative
- Applicare strategie di pricing
- Simulare scenari di revenue management
- Gestire eventi e loro impatto sui prezzi

### 3. Gestione Strutture Ricettive
- **Unità Abitative (UA)**: Gestione tipologie camere
- **Categorie UA**: Classificazione delle unità
- **Sigle UA**: Codici identificativi
- **Mappatura Sigle**: Integrazione con sistemi esterni

### 4. Sistema di Costi e Ricavi
- **Costi Fissi e Marginali**: Gestione completa dei costi
- **Strategie Cliente**: Personalizzazione per cliente
- **Margine Operativo Lordo (MOL)**: Calcolo automatico
- **Revenue Optimization**: Ottimizzazione ricavi

## Store Pinia - Gestione Stato

### Store Principali

#### `useSimulatore.js`
Store centrale per la simulazione di prezzi:
- Gestione calendario prezzi/disponibilità
- Calcoli di revenue management
- Persistenza dati in IndexedDB
- Integrazione con tutti gli altri store

#### `useSigleuaStore.js`
Gestione sigle unità abitative:
- Mappatura sigle per categoria
- Prezzi minimi/massimi per sigla
- Configurazione persone e sconti

#### `useClienteStore.js`
Dati cliente e struttura:
- Informazioni struttura ricettiva
- Configurazioni specifiche cliente
- Dati di contatto e fatturazione

#### `useCostiStore.js`
Gestione costi operativi:
- Costi fissi e marginali
- Costi per servizi (colazione, ristorante)
- Coefficienti di disoccupazione

#### Altri Store Specializzati
- `useDateStrutturaStore`: Gestione periodi e stagionalità
- `useEventiStore`: Eventi e loro impatto sui prezzi
- `useFilterFocusStore`: Filtri e visualizzazioni
- `useVariazionePrezziOccupazioneStore`: Variazioni prezzo per occupazione

## Componenti Chiave

### Simulatore Interface
- **Tabella Calendario**: Visualizzazione prezzi per data/UA
- **Filtri Focus**: Controlli per visualizzazione dati
- **Input Dinamici**: Modifica prezzi e disponibilità in tempo reale
- **Drag & Scroll**: Navigazione fluida del calendario

### Gestione Dati
- **Google Sheets Integration**: Import/export dati
- **File Upload**: Caricamento prenotazioni da CSV/TXT
- **IndexedDB**: Persistenza locale dati

## Configurazione e Deploy

### Ambiente di Sviluppo
```bash
# Installazione dipendenze
pnpm install

# Sviluppo
pnpm dev

# Build produzione
pnpm build
```

### Configurazione API
- **Base URL**: AWS Lambda (eu-north-1)
- **Autenticazione**: Bearer token
- **Endpoints**: RESTful API per tutte le operazioni

### PWA Support
- Service Worker configurato
- Cache strategy per assets
- Supporto offline limitato

## Sicurezza e Performance

### Sicurezza
- Middleware di autenticazione globale
- Controllo accessi basato su ruoli (CASL)
- Validazione input lato client e server
- Gestione sicura dei token JWT

### Performance
- Lazy loading componenti
- Virtualizzazione tabelle grandi
- Caching intelligente con IndexedDB
- Ottimizzazione bundle con Vite

## Internazionalizzazione

- **Supporto multilingua**: Italiano, Inglese, Francese
- **Vue I18n**: Gestione traduzioni
- **Locale dinamico**: Cambio lingua runtime

## Documentazione Tecnica

### File di Documentazione
- `_docs/stores.md`: Documentazione dettagliata store
- `_docs/use_simulatore.md`: Guida uso simulatore
- `_docs/formule_calendario.md`: Formule di calcolo
- `_docs/installazione/`: Guide installazione

### Pattern di Sviluppo
- **Composables**: Logica riutilizzabile
- **Store Pattern**: Gestione stato centralizzata
- **Component Composition**: Componenti modulari
- **TypeScript**: Tipizzazione graduale

## Integrazione Sistemi Esterni

### Channel Manager
- Mappatura codici UA per canali esterni
- Sincronizzazione prezzi e disponibilità
- Gestione booking engine

### Gestionale Alberghiero
- Import dati prenotazioni
- Sincronizzazione unità abitative
- Export reportistica

## Roadmap e Sviluppi Futuri

### Funzionalità Pianificate
- Dashboard analytics avanzate
- Machine learning per pricing
- API pubbliche per integrazioni
- Mobile app companion

### Miglioramenti Tecnici
- Migrazione completa TypeScript
- Test coverage aumentato
- Performance monitoring
- Deployment automatizzato

---

**Versione Corrente**: 1.1.78  
**Ultimo Aggiornamento**: 2024  
**Licenza**: Proprietaria  
**Sviluppato da**: Team RevD
