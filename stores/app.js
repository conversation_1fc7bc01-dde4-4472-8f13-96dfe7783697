import { defineStore } from 'pinia';

export const useAppStore = defineStore('app', {
    state: () => ({
        isLoading: false,
        numeroCliente: null,
        cliente: null,
        customerLastUpdate: null,
        alert: {show: false, message: '', color: 'success'}
    }),
    getters: {
        getCliente: (state) => state.cliente,
        isOscurato: (stato) => {
            if(!stato.cliente) return false
            return stato.cliente.oscurato
        }
    },
    actions: {
        async loadCliente(numeroCliente) {
            this.isLoading=true
            let { data } = await useApi(createUrl('/resources', {
                query: {
                    tb: 'clienti',
                    pk: 'numero_cliente',
                    sk: this.numeroCliente
                }
            }))

            this.isLoading=false
            return  data.value.body
        },
        async setCliente(numeroCliente) {

            let { data } = await useApi(createUrl('/resources', {
                query: {
                    tb: 'clienti',
                    pk: 'numero_cliente',
                    sk: numeroCliente
                }
            }))


            this.cliente = data.value.body
            return this.cliente
        },
        setLoading(flag) {  this.isLoading = flag },
        async setNumeroCliente(numeroCliente) {
            this.isLoading=true
            await this.setCliente(numeroCliente)
                .then(()=>{
                    this.numeroCliente = numeroCliente
                    this.isLoading=false
                })


        },
        showAlert(message, timeout = 2000, color = 'primary') {
            this.alert = { show: true, message, color, timeout };
        },
        updateCustomerLastUpdate() {  this.customerLastUpdate = Date.now() }
    }
});
