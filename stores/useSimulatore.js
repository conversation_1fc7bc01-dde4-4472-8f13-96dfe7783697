import { usePrenotazioniDownloadStore } from "@/stores/usePrenotazioniDownloadStore.js";
import { useVariazionePrezziOccupazioneStore } from '@/stores/useVariazionePrezziOccupazioneStore';
import { defineStore } from 'pinia';
import { useCategorieUaStore } from './useCategorieUaStore';
import { useClienteStore } from './useClienteStore';
import { useCostiStore } from './useCostiStore';
import { useDateStrutturaStore } from './useDateStrutturaStore';
import { useEventiStore } from './useEventiStore';
import { useImpostazioniSimulatore } from './useImpostazioniSimulatore';
import { useMappaSigleuaStore } from './useMappaSigleuaStore';
import { useSigleuaStore } from './useSigleuaStore';
import { useStrategieClienteStore } from './useStrategieClienteStore';

export const useSimulatore = defineStore('simulatore', {
    state: () => ({
        eventi: [],
        isDebugMode: false,
        isTraceMode: false,
        totale_ua_per_sigla: {},
        numero_cliente: '',
        prezzo_listino_per_numero_unita: {},
        totale_prezzo_listino_per_numero_unita: 0,
        peso_su_tipologia_ua_auto: {},
        costo_su_tipologia_ua_auto: {},
        prezzo_minimo_per_sigla_ua: {},
        costo_totale: 0,
        costo_totale_eventi: 0,
        costo_totale_ricavi_aggiuntivi:0,
        costi_fissi_marginali_eventi: 0,
        sumCostiFissi: 0,
        sumCostiMarginali: 0,
        coefficiente_disoccupazione: 0,
        mol_reale: 0,
        mol_forzato: 0,
        withIva: false,
        calendario: {},
        percColonna: {},
        sigleUa: [],
        sigle: [],
    }),
    getters: {
        // Restituisce una mappa: { [siglaUa]: {categoria, nUA, prezzo_minimo, siglaUa, persone, personeAggiuntive} }
        sigleUaMapBySiglaUa(state) {
            // Usa lo stato sigle già filtrato senza uaMadre
            const map = {};
            state.sigle.forEach(s => {
                map[s.siglaUA] = {
                    categoria: s.categoria,
                    nUA: s.nUA,
                    prezzo_minimo: s.prezzo_minimo,
                    siglaUa: s.siglaUA,
                    scontoLetto1: s.sconto1Letto || 0,
                    scontoLetto2: s.sconto2Letto || 0,
                    scontoLetto3: s.sconto3Letto || 0,
                    scontoLetto4: s.sconto4Letto || 0,
                    scontoLetto5: s.sconto5Letto || 0,
                    scontoLetto6: s.sconto6Letto || 0,
                    persone: s.persone,
                    personeAggiuntive: s.personeAggiuntive
                };
            });
            return map;
        },
    },
    actions: {
        async parseReservations(reservations) {
            // TODO: Implementare la logica per parsare le prenotazioni
            console.log('Parsing reservations:', reservations);
            return [];
        },

        async saveValue(key, sigla, param, newValue) {
            const day = this.calendario[key];
            if (day) {
                const siglaData = day.sigle.find((s) => s.siglaUa === sigla);
                if (siglaData) {
                    siglaData[param] = newValue * 1; // Assicurati che sia un numero
                    await this.saveCalendarioToIndexedDB(toRaw(this.calendario)); // Salva in IndexedDB
                    if (this.isDebugMode) {
                        console.log("Salvato valore", newValue, "per sigla", sigla, "parametro", param, "chiave", key);
                    }
                } else {
                    console.error(`Sigla ${sigla} non trovata per la chiave ${key}`);
                }
            } else {
                console.error(`Chiave ${key} mancante nel calendario`);
            }
        },

        // BOOK: setup
        async setup(numeroCliente) {
            const costiStore = useCostiStore();
            const sigleuaStore = useSigleuaStore();
            const strategieClienteStore = useStrategieClienteStore();
            const dateStrutturaStore = useDateStrutturaStore();
            const eventiStore = useEventiStore();
            const clienteStore = useClienteStore();
            const impostazioniSimulatoreStore = useImpostazioniSimulatore();
            const mappaSigleuaStore = useMappaSigleuaStore();
            const categorieUaStore = useCategorieUaStore();
            const variazionePrezziOccupazioneStore = useVariazionePrezziOccupazioneStore();

            try {
                if (this.isDebugMode) console.log('Inizio setup simulatore per cliente:', numeroCliente);
                this.numero_cliente = numeroCliente;
                const calendarioPromise = this.loadCalendarioFromIndexedDB();
                const useCache = false;

                await Promise.all([
                    mappaSigleuaStore.loadMappaSigleua(numeroCliente, useCache),
                    clienteStore.loadCliente(numeroCliente, useCache),
                    impostazioniSimulatoreStore.loadImpostazioniSimulatore(numeroCliente, useCache),
                    eventiStore.loadEventi(numeroCliente, useCache),
                    strategieClienteStore.loadStrategieCliente(numeroCliente, useCache),
                    sigleuaStore.loadSigleua(numeroCliente, useCache),
                    costiStore.loadCosti(numeroCliente, useCache),
                    dateStrutturaStore.loadDateStruttura(numeroCliente, useCache),
                    categorieUaStore.loadCategorieUa(numeroCliente, useCache),
                    variazionePrezziOccupazioneStore.load(numeroCliente)
                ]);
                const calendario = await calendarioPromise;
                const isLoadedCalendario = (calendario && Object.keys(calendario).length > 0);

                clienteStore.processCliente(numeroCliente);
                dateStrutturaStore.processDateStruttura();
                this.peso_per_periodo = dateStrutturaStore.peso_per_periodo;

                eventiStore.processEventi();
                this.eventi = eventiStore.eventi;
                this.coefficiente_disoccupazione = strategieClienteStore.coefficiente_disoccupazione;

                this.sigleUa = sigleuaStore.sigleUa || [];
                this.sigle = sigleuaStore.sigleua.filter((sigla) => !sigla.uaMadre);
                sigleuaStore.processSigleua();
                this.totale_ua_per_sigla = sigleuaStore.totale_ua_per_sigla || {};
                this.prezzo_minimo_per_sigla_ua = sigleuaStore.prezzo_minimo_per_sigla_ua || {};

                // BOOK: costi
                costiStore.processCosti(this.coefficiente_disoccupazione);
                this.costo_totale = costiStore.costi_presunti;
                this.withIva = costiStore.withIva;
                this.sumCostiFissi = costiStore.sumCostiFissi;
                this.sumCostiMarginali = costiStore.sumCostiMarginali;
                let costiFissiEMarginali = this.sumCostiFissi + this.sumCostiMarginali;
                let costiFissiEMarginali40Perc = costiFissiEMarginali * this.coefficiente_disoccupazione/100;

                const tipoEventi = impostazioniSimulatoreStore.tipo_eventi;
                this.costo_totale_eventi = eventiStore.costoTotale;
                this.costi_fissi_marginali_eventi = costiFissiEMarginali + this.costo_totale_eventi;

                // BOOK: ricavi
                this.ricavi_anno_precedente = strategieClienteStore.ricavi_anno_precedente|0;
                this.ricavi_aggiuntivi = strategieClienteStore.ricavi_aggiuntivi;

                // BOOK: mol
                this.mol_reale = (this.ricavi_anno_precedente|0 + this.ricavi_aggiuntivi|0-this.costi_fissi_marginali_eventi|0)/(this.ricavi_anno_precedente|0+this.ricavi_aggiuntivi|1)*100

                if (this.isDebugMode) {
                    console.groupCollapsed("MOL")
                    console.log('Calcolo MOL Reale:');
                    console.log('Ricavi Anno Precedente:', this.ricavi_anno_precedente);
                    console.log('Ricavi Aggiuntivi:', this.ricavi_aggiuntivi);
                    console.log('Costi Fissi Marginali Eventi:', this.costi_fissi_marginali_eventi);
                    console.log('MOL Reale:', this.mol_reale, "(ricavi_anno_precedente + ricavi_aggiuntivi - costi_fissi_marginali_eventi)/(ricavi_anno_precedente+ricavi_aggiuntivi)*100");
                    console.log('MOL Forzato:', this.mol_forzato);
                    console.groupEnd();
                }

                this.mol_forzato = impostazioniSimulatoreStore.mol_libero/100;



                // Se sono 'Eventi Unici', il costo degli eventi è già distribuito nelle date; altrimenti si somma il costo totale degli eventi
                const costoEventi = (tipoEventi === "Eventi Unici") ? 0 : eventiStore.costoTotale;

                // calcolo totale dei ricavi aggiuntivi, includendo il costo eventi solo per 'Eventi Distribuiti'
                this.costo_totale_ricavi_aggiuntivi =
                    this.costo_totale + strategieClienteStore.ricavi_aggiuntivi + costiFissiEMarginali40Perc + costoEventi;

                // BOOK: per forzare la generazione del calendario (isLoadedCalendario && false) altrimenti (isLoadedCalendario)
                if (isLoadedCalendario && false) {
                    this.calendario = calendario;
                    console.log('Calendario caricato da IndexedDB:');
                } else {
                    this.calculatePrezzoListinoPerNumeroUnita();
                    this.calculatePesoSuTipologiaUaAuto();
                    await this.generateCalendario();
                }

                if (this.isDebugMode) console.log('Setup completato');
            } catch (error) {
                console.error('Errore durante il setup del simulatore:', error);
            }
        },

        // BOOK: generateCalendario
        async generateCalendario() {
            console.log('Generazione calendario...', 'cliente:', this.numero_cliente);
            this.calendario = {};
            let doLog = true;
            const prenotazioniDownloadStore=usePrenotazioniDownloadStore();

            let prenotazioni = await usePrenotazioniDownloadStore().leggiPrenotazioniDaDB(this.numero_cliente)
            prenotazioni = await usePrenotazioniDownloadStore().leggiPrenotazioniDaDB(this.numero_cliente)

            //console.log("---", prenotazioni)
            // BOOK: carica mappa prezzi per occupazione
            const variazionePrezziOccupazioneStore = useVariazionePrezziOccupazioneStore();
            const mappaPrezziPerOccupazione =  variazionePrezziOccupazioneStore.mappaOccupazione;


            // Funzione helper per recuperare il tipo tariffa da eventuali dati precedenti
            const calcolaTipoTariffa = (calendarioBackup, calKey) =>
                calendarioBackup[calKey] ? calendarioBackup[calKey].tipoTariffa : '-';

            // --- Funzioni helper per i calcoli ---
            const getOccupancy = (sigla,calKey, prenotazioni) => {
                const calKeySigla = calKey+"-"+sigla; 
                //D_20250427-SM3
                if(!prenotazioni[calKeySigla]) {
                    //console.log("getOccupancy",calKeySigla,"non trovata");
                    return 0;
                }
                //console.log("getOccupancy",calKeySigla,"trovata");
                const occ =  prenotazioni[calKeySigla].percOccupazione || 0;
                return Number((occ / 100).toFixed(2));
            };

            const impostazioniSimulatoreStore = useImpostazioniSimulatore();
            const gestione_costi = impostazioniSimulatoreStore.gestione_costi
            const tipo_eventi = impostazioniSimulatoreStore.tipo_eventi;
            const strategieCliente = useStrategieClienteStore()

            // BOOK: ricavi
            const ricavi_anno_precedente = strategieCliente.ricavi_anno_precedente|0;
            const ricavi_aggiuntivi = strategieCliente.ricavi_aggiuntivi|0;


            // BOOK: usa la mappa dell'occupazione
            function getModificaDaOccupazione(occupancy) {
                occupancy=occupancy*100;
                const v= variazionePrezziOccupazioneStore.getVariazioneByOccupancy(occupancy);
                //console.log("occupancy", occupancy,"-- var", v)
                return v;

            }

            const applyPriceModifications = (basePrice, garantito, modificaperc, prezzoforzato) => {
                let finalPrice = basePrice;
                if (prezzoforzato && prezzoforzato > 0) {
                    finalPrice = prezzoforzato;
                } else {
                    if (garantito && finalPrice < garantito) {
                        finalPrice = garantito;
                    }
                    if (modificaperc && modificaperc !== 0) {
                        finalPrice = finalPrice * (1 + modificaperc / 100);
                    }
                }
                return finalPrice;
            };
            const calcolaCostoAutomaticoPerUA = (sigla, percOccupazione = 0, totalPesoApertura = 1) => {
                const peso = this.peso_su_tipologia_ua_auto[sigla.siglaUA] || 0;
                const occupazione = percOccupazione || 0;
                const valMaxSuDataTraPesoEOccupazione = peso > occupazione ? peso : occupazione;
                const costoAnnualeTotale = this.costo_su_tipologia_ua_auto[sigla.siglaUA] || 0;
                const nUA = parseFloat(sigla.nUA) || 1;
                return costoAnnualeTotale * (valMaxSuDataTraPesoEOccupazione / totalPesoApertura) / nUA;
            };
            const calcolaCosto = (sigla, stato, totalOpenDays, ricavi = 0, occupancy, totalPesoApertura = 1) => {
                if (stato !== "A") return '-';
                const costo = calcolaCostoAutomaticoPerUA(sigla, occupancy, totalPesoApertura);
                return Math.floor(costo);
            };

            // BOOK: mup
            const calcolaPrezzoMup=(costoFocus, stato, molReale, molForzato) =>{
                if (stato !== "A") return '-';
                let molDaUsare = (molReale > molForzato) ? molReale : molForzato;
                return costoFocus * molDaUsare+costoFocus;
            }

            // BOOK: prezzo revenue
            const calculatePrezzoRevenue=(mup, disoccupazione, date='-') => {
                const br = getModificaDaOccupazione(disoccupazione);
                const cr = mup * br;

                //console.log("calcolaPrezzoRevenue","Data",date,"mup",mup, "disoccupazione", disoccupazione,"coeff. disoccupazione", br,"revenue",cr)

                return cr.toFixed(0);
            }

            // BOOK: funzioni d e f g x j k n o p
            const computeD = (sigla) => {
                const nUA = parseFloat(sigla.nUA);
                const prezzoMinimo = parseFloat(sigla.prezzo_minimo);

                if (isNaN(nUA) || isNaN(prezzoMinimo)) {
                    console.log(`Attenzione in computeD: valori non numerici per sigla = ${JSON.stringify(sigla)}, nUA = ${sigla.nUA}, prezzo_minimo = ${sigla.prezzo_minimo}`);
                    return 0; // Ritorna 0 per evitare NaN
                }
                const persone = sigla.persone || 1;
                if(persone === 0) {
                    console.log("computeD persone = 0",sigla.nUA)
                    return 0;
                }
                return nUA * prezzoMinimo/ persone;
            };
            const computeSumDAll = (categories) => {
                if (!Array.isArray(categories)) {
                    console.log(`Attenzione in computeSumDAll: categories non è un array valido. categories = ${JSON.stringify(categories)}`);
                    return 0;
                }

                const allUAs = categories.flatMap(cat => {
                    if (!Array.isArray(cat.UAs)) {
                        console.log(`Attenzione in computeSumDAll: UAs non è un array per categoria ${JSON.stringify(cat)}`);
                        return [];
                    }
                    return cat.UAs;
                });

                return allUAs.reduce((sum, sigla) => {
                    const d = computeD(sigla);
                    return sum + d;
                }, 0);
            };
            const computeE = (sigla, sumDAll) => {
                const result = (100 * computeD(sigla)) / sumDAll;
                if (isNaN(result)) {
                    console.log(`Attenzione: risultato 'computeE' è NaN. Parametri: sigla , sumDAll = ${sumDAll}`);
                }
                return result;
            };
            const computeF = (category, sumDAll) => {
                const result = category.UAs.reduce((sum, s) => sum + ((100 * (parseFloat(s.nUA) * parseFloat(s.prezzo_minimo))) / sumDAll), 0); // computeF
                if (isNaN(result)) {
                    console.log(`Attenzione: risultato 'computeF' è NaN. Parametri: category , sumDAll = ${sumDAll}`);
                }
                return result;
            };
            const computeG = (sigla, f) => {
                const e = Number(computeE(sigla));
                const g = (100 * e) / f; // computeG
                if (isNaN(g)) {
                    console.log(`Attenzione: risultato 'g' è NaN. Parametri: e = ${e}, f = ${f}`);
                }
                return g;
            };
            const computeX = (sigla, h, sumDAll) => (h * computeE(sigla, sumDAll)) / 100;
            const computeJ = (i, h) => (i * h) / 100;
            const computeK = (j, g) => (j * g) / 100;
            const computeN = (l, m) => Math.max(l, m);
            const computeO = (category, nDate, pesoData) => {
                // Controlli iniziali sui parametri
                if (category === null || category === undefined) {
                    console.log(`Attenzione in computeO: parametro 'category' è ${category === null ? 'null' : 'undefined'}`);
                    return 0; // Ritorna 0 per evitare errori downstream
                }
                if (nDate === null || nDate === undefined) {
                    console.log(`Attenzione in computeO: parametro 'nDate' è ${nDate === null ? 'null' : 'undefined'}`);
                    return 0;
                }
                if (pesoData === null || isNaN(pesoData)) {
                    console.log(`Attenzione in computeO: parametro 'pesoData' è ${pesoData === null ? 'null' : 'NaN'}`);
                    return 0;
                }

                return category.openDates.reduce((sum, date) => {
                    const l = nDate[date] || 0;
                    const m = pesoData * 100;
                    const n = computeN(l, m);
                    return sum + n;
                }, 0);
            };
            const computeP = (date, o, nDate, pesoData) => {
                // Controlli sui parametri in ingresso
                if (date === null || date === undefined) {
                    console.log(`Attenzione in computeP: parametro 'date' è ${date === null ? 'null' : 'undefined'}`);
                    return 0;
                }
                if (o === null || isNaN(o)) {
                    console.log(`Attenzione in computeP: parametro 'o' è ${o === null ? 'null' : 'NaN'}`);
                    return 0;
                }
                if (nDate === null || nDate === undefined) {
                    console.log(`Attenzione in computeP: parametro 'nDate' è ${nDate === null ? 'null' : 'undefined'}`);
                    return 0;
                }
                if (pesoData === null || isNaN(pesoData)) {
                    console.log(`Attenzione in computeP: parametro 'pesoData' è ${pesoData === null ? 'null' : 'NaN'}`);
                    return 0;
                }

                // Calcolo di l
                const l = nDate[date] || 0;
                if (l === null || isNaN(l)) {
                    console.log(`Attenzione in computeP: l è ${l === null ? 'null' : 'NaN'} per date = ${date}, nDate = ${JSON.stringify(nDate)}`);
                }

                // Calcolo di m
                const m = pesoData * 100;
                if (m === null || isNaN(m)) {
                    console.log(`Attenzione in computeP: m è ${m === null ? 'null' : 'NaN'}, pesoData = ${pesoData}`);
                }

                // Calcolo di n
                const n = computeN(l, m);
                if (n === null || isNaN(n)) {
                    console.log(`Attenzione in computeP: n è ${n === null ? 'null' : 'NaN'}, l = ${l}, m = ${m}`);
                }

                // Calcolo finale
                const result = (100 * n) / o;
                if (isNaN(result)) {
                    console.log(`Attenzione in computeP: risultato è NaN, n = ${n}, o = ${o}, formula = (100 * ${n}) / ${o}`);
                    return 0;
                }

                return result;
            };
            const computeCostManual = (k, p, b) => (((k * p) / 100) / b);
            const computeCostAuto = (x, p, b) => (((x * p) / 100) / b);

            // BOOK: determinaCostoFocus
            const determinaCostoFocus = (sigla, categories, costoTotaleStruttura, date, nDate, category, pesoData, occupancy) => {
                let h = costoTotaleStruttura;
                let doLog = true;
                const b = parseFloat(sigla.nUA);

                const categorieUaStore = useCategorieUaStore();
                const pesiPerSigla = categorieUaStore.getPesiPerSigla;
                const pesoCategoria = pesiPerSigla[sigla.categoria] || 1;
                const totalePesiCategorie = Object.values(pesiPerSigla).reduce((sum, peso) => sum + peso, 0) || 1;
                const pesoCategoriaNormalizzato = pesoCategoria / totalePesiCategorie;

                const persone = parseFloat(sigla.persone) || 1; // Assicuriamoci che sia un numero
                // Calcoli inline usando la lettera precedente
                const sumDAll = categories.flatMap(cat => cat.UAs).reduce((sum, s) => sum + (parseFloat(s.nUA) * parseFloat(s.prezzo_minimo) / parseFloat(s.persone || 1)), 0);

                const d = (parseFloat(sigla.nUA) * parseFloat(sigla.prezzo_minimo)) / persone; // computeD
                const e = (100 * d) / sumDAll; // computeE usando d
                const f = category.UAs.reduce((sum, s) => sum + ((100 * (parseFloat(s.nUA) * parseFloat(s.prezzo_minimo))) / sumDAll), 0); // computeF

                const i = pesoCategoria;
                let g = gestione_costi === "manuale" ? (100 * e) / i : (100 * e) / f; // computeG condizionale


                let additionalCost = 0;
                if (tipo_eventi === "Eventi Unici") {
                    // Recupera gli eventi che coprono la data corrente
                    const eventiDelGiorno = useEventiStore().eventi.filter(ev => {
                        return date >= ev.start_date && date <= ev.end_date;
                    });

                    // Calcola il costo per giorno per eventi unici
                    eventiDelGiorno.forEach(ev => {
                        // A: Periodo in giorni = differenza tra ev.end_date e ev.start_date (minimo 1)
                        const start = new Date(ev.start_date);
                        const end = new Date(ev.end_date);
                        const A = Math.max(1, Math.round((end - start) / (1000 * 60 * 60 * 24)));
                        const B = ev.costo; // B: Costo totale evento
                        const C = B / A;    // C: Costo per giorno = B / A
                        // D: g rappresenta il peso per sigla UA su data (passato alla funzione)
                        // E: Costo evento su data per sigla UA = ((((B/A) * g)/100) / b)
                        additionalCost += (((C * g) / 100) / b);

                        if(this.isDebugMode) {
                            console.groupCollapsed("costo addizionale")
                            console.log("A", A)
                            console.log("B ev.costo:", B)
                            console.log("C   B / A", C)
                            console.log("g   ", g)
                            console.log("b  numero UA ", b)
                            console.log("additionalCost (((C * g) / 100) / b)", additionalCost)
                            console.groupEnd()
                        }

                    });
                    // Aggiunge il costo addizionale a h

                }

                h += additionalCost;

                const j = (i * h) / 100; // computeJ usando i
                const k = (j * g) / 100; // computeK usando j
                const l = (nDate[date] || 0) * 10;
                const m = occupancy * 100;
                const n = Math.max(l, m); // computeN usando l
                // TODO: rivedere il calcolo di o - occupacy è quello di ogni giorno per categoria e non è fisso
                let  o = category.openDates.reduce((sum, d) => sum + Math.max((nDate[d] || 0), (occupancy * 100)), 0); // computeO usando nDate e pesoData
                o = 11534

                const p = (100 * n) / o; // computeP usando n
                const xBase = (h * e) / 100; // computeX usando e
                const x = xBase * pesoCategoriaNormalizzato;


                let costoFocus = 0;
                if (gestione_costi === "automatico") {
                    costoFocus = ((x * p) / 100) / b; // computeCostAuto usando x
                } else {
                    costoFocus = ((k * p) / 100) / b; // computeAuto usando k
                }

                costoFocus+=additionalCost;


                // Fase 2: Logging diretto con console.groupCollapsed
                const curDate = '2025-04-30';

                if (this.isDebugMode && date === curDate) {
                  const logParams = {
                    sigla,
                    categories,
                    h,
                    date,
                    nDate,
                    category,
                    pesoData,
                    gestione_costi,
                    tipo_eventi,
                    pesoCategoria,
                    totalePesiCategorie,
                    pesoCategoriaNormalizzato,
                    sumDAll,
                    b,
                    d,
                    e,
                    f,
                    g,
                    i,
                    j,
                    k,
                    l,
                    m,
                    n,
                    o,
                    p,
                    xBase,
                    x,
                    costoFocus,
                    curDate
                  };
                  const loggerPath = './simulatore/simulatoreLogger.js';
                  import(loggerPath).then(module => {
                    module.logDettaglioCalcoloSigla(logParams);
                  });
                }

                doLog = false;
                return costoFocus;
            };

            // --- Backup e logica principale ---
            let calendarioBackup = {};
            for (const key in this.calendario) {
                if (this.calendario[key].tipoTariffa && this.calendario[key].tipoTariffa !== '-') {
                    calendarioBackup[key] = { tipoTariffa: this.calendario[key].tipoTariffa };
                }
            }

            try {

                const { $dayjs } = useNuxtApp();
                const todayYYYY_MM_DD = $dayjs().format('YYYY-MM-DD');

                const dateStrutturaStore = useDateStrutturaStore();
                const sigleuaStore = useSigleuaStore();
                const categorieUaStore = useCategorieUaStore();

                const dateArray = dateStrutturaStore.date;
                const sigle = sigleuaStore.sigleua.filter((sigla) => !sigla.uaMadre);

                if (!dateArray || dateArray.length === 0) {
                    console.log('Nessun periodo trovato in useDateStrutturaStore.');
                    return {};
                }
                if (sigle.length === 0) {
                    console.log('Nessuna sigla valida trovata in useSigleuaStore.');
                    return {};
                }

                const pesiPerSigla = categorieUaStore.getPesiPerSigla;

                const categories = Object.entries(pesiPerSigla).map(([sigla, peso]) => ({
                    categoria: sigla,
                    UAs: sigle
                        .filter(s => s.categoria === sigla)
                        .map(s => ({
                            categoria: s.categoria,
                            nUA: s.nUA,
                            prezzo_minimo: s.prezzo_minimo,
                            siglaUa: s.siglaUA, // Nota: "siglaUa" invece di "siglaUA" per uniformità con la tua richiesta
                            persone: s.persone,
                            personeAggiuntive: s.personeAggiuntive
                        })),
                    peso: peso,
                    openDates: dateArray
                        .filter(p => p.struttura === 'A')
                        .map(p => $dayjs(p.start_date).format('YYYY-MM-DD'))
                }));

                let totalPesoApertura = 0;
                const nDate = {};

                // BOOK: crea pesi per date
                for (const periodo of dateArray) {
                    const start = $dayjs(periodo.start_date);
                    const end = $dayjs(periodo.end_date);
                    if (periodo.struttura === 'A') {
                        const numDays = end.diff(start, 'day') + 1;
                        totalPesoApertura += (Number(periodo.peso_periodo) || 0) * numDays;
                        let current = start;
                        while (current.isSameOrBefore(end, 'day')) {
                            const dateStr = current.format('YYYY-MM-DD');
                            nDate[dateStr] = (Number(periodo.peso_periodo) || 0) // verifica scala peso * 10;
                            current = current.add(1, 'day');
                        }
                    }
                }

                const minDate = $dayjs.min(dateArray.map((periodo) => $dayjs(periodo.start_date)));
                const maxDate = $dayjs.max(dateArray.map((periodo) => $dayjs(periodo.end_date)));

                const calendario = {};
                let currentDate = minDate.startOf('day');
                let cumulativeOpenDays = 0;

                // BOOK: ciclo sulle date
                while (currentDate.isSameOrBefore(maxDate, 'day')) {
                    let peso = 0;
                    let stato = 'C';
                    const calKey = 'D_' + currentDate.format('YYYYMMDD');
                    const tipoTariffa = calcolaTipoTariffa(calendarioBackup, calKey);
                    const dateStr = currentDate.format('YYYY-MM-DD');

                    const elaborateDate = (dateStr>=todayYYYY_MM_DD);

                    for (const periodo of dateArray) {
                        const start = $dayjs(periodo.start_date);
                        const end = $dayjs(periodo.end_date);
                        if (currentDate.isBetween(start, end, 'day', '[]')) {
                            peso = Number(periodo.peso_periodo) || 0;
                            stato = periodo.struttura;
                            break;
                        }
                    }

                    const pesoData = (peso * 10) / 100;
                    if (stato === 'A')  cumulativeOpenDays++;

                    const siglePerData = [];
                    const siglePerDataKeys = {};



                    sigle.forEach((sigla) => {

                        const siglaStato = stato;
                        if (siglaStato !== 'A' || !elaborateDate) {
                            const siglaData = {
                                siglaUa: sigla.siglaUA,
                                categoria: sigla.categoria,
                                stato: siglaStato,
                                costo: '-',
                                prezzomup: '-',
                                prezzorevenue: '-',
                                prezzorevenue1: '-',
                                prezzorevenue2: '-',
                                prezzorevenue3: '-',
                                prezzorevenue4: '-',
                                prezzorevenue5: '-',
                                prezzorevenue6: '-',
                                prezzorevenue7: '-',
                                prezzorevenue8: '-',
                                concorrenza: '-',
                                occupazioneperc: '-',
                                profitto: '-',
                                rmc: '-',
                                revpar: '-',
                                garantito: '',
                                modificaperc: '',
                                prezzoforzato: '',
                                aprezzo_ori: '-',
                                aprezzo: '-',
                                aprezzo1: '-',
                                aprezzo2: '-',
                                aprezzo3: '-',
                                aprezzo4: '-',
                                aprezzo5: '-',
                                aprezzo6: '-',
                                aprezzo7: '-',
                                aprezzo8: '-',
                                peso: '-'
                            };
                            siglePerData.push(siglaData);
                            siglePerDataKeys[sigla.siglaUA] = siglaData;
                            return;
                        }

                        const occupancy = getOccupancy(sigla.siglaUA,calKey, prenotazioni);
                        const category = categories.find(cat => cat.categoria === sigla.categoria);
                        const costoFocus = determinaCostoFocus(sigla, categories, this.costo_totale_ricavi_aggiuntivi, dateStr, nDate, category, pesoData, occupancy);
                        const costoGiornaliero = Math.floor(costoFocus); // Usa determinaCostoFocus
                        const prezzomup = calcolaPrezzoMup(Math.floor(costoFocus), siglaStato,this.mol_reale, this.mol_forzato);
                        const prezzorevenue = calculatePrezzoRevenue(prezzomup, occupancy, calKey);
                        const garantito = parseFloat(sigla.garantito) || 0;
                        const modificaperc = parseFloat(sigla.modificaperc) || 0;
                        const prezzoforzato = parseFloat(sigla.prezzoforzato) || 0;
                        const finalPrice = applyPriceModifications(prezzorevenue, garantito, modificaperc, prezzoforzato);

                        const siglaData = {
                            siglaUa: sigla.siglaUA,
                            categoria: sigla.categoria,
                            stato: siglaStato,
                            costo: costoGiornaliero,
                            prezzomup: prezzomup.toFixed(0),
                            prezzorevenue: this.prezziRevenueN(0, prezzorevenue, sigla.siglaUA),
                            prezzorevenue1: this.prezziRevenueN(1, prezzorevenue, sigla.siglaUA),
                            prezzorevenue2: this.prezziRevenueN(2, prezzorevenue, sigla.siglaUA),
                            prezzorevenue3: this.prezziRevenueN(3, prezzorevenue, sigla.siglaUA),
                            prezzorevenue4: this.prezziRevenueN(4, prezzorevenue, sigla.siglaUA),
                            prezzorevenue5: this.prezziRevenueN(5, prezzorevenue, sigla.siglaUA),
                            prezzorevenue6: this.prezziRevenueN(6, prezzorevenue, sigla.siglaUA),
                            prezzorevenue7: this.prezziRevenueN(7, prezzorevenue, sigla.siglaUA),
                            prezzorevenue8: this.prezziRevenueN(8, prezzorevenue, sigla.siglaUA),
                            concorrenza: 4,
                            occupazioneperc: (occupancy * 100).toFixed(0) + '%',
                            profitto: finalPrice - costoGiornaliero,
                            rmc: finalPrice,
                            revpar: finalPrice,
                            garantito: garantito,
                            modificaperc: modificaperc,
                            prezzoforzato: prezzoforzato,
                            aprezzo_ori: finalPrice,
                            aprezzo: this.finalPriceN(0, finalPrice, sigla.siglaUA),
                            aprezzo1: this.finalPriceN(1, finalPrice, sigla.siglaUA),
                            aprezzo2: this.finalPriceN(2, finalPrice, sigla.siglaUA),
                            aprezzo3: this.finalPriceN(3, finalPrice, sigla.siglaUA),
                            aprezzo4: this.finalPriceN(4, finalPrice, sigla.siglaUA),
                            aprezzo5: this.finalPriceN(5, finalPrice, sigla.siglaUA),
                            aprezzo6: this.finalPriceN(6, finalPrice, sigla.siglaUA),
                            aprezzo7: this.finalPriceN(7, finalPrice, sigla.siglaUA),
                            aprezzo8: this.finalPriceN(8, finalPrice, sigla.siglaUA),
                            peso: pesoData
                        };

                        siglePerData.push(siglaData);
                        siglePerDataKeys[sigla.siglaUA] = siglaData;
                    });

                    calendario[calKey] = {
                        data: dateStr,
                        pesoData: pesoData,
                        openDays: cumulativeOpenDays,
                        stato: stato,
                        tipoTariffa: tipoTariffa,
                        modificaPerc: '',
                        sigle: siglePerData,
                        sigleKeys: siglePerDataKeys,
                    };

                    currentDate = currentDate.add(1, 'day');
                }

                this.calendario = calendario;
                this.saveCalendarioToIndexedDB(calendario);
                return this.calendario;
            } catch (error) {
                console.error('Errore durante la generazione del calendario:', error);
                return {};
            }
        },

        async loadCalendarioFromIndexedDB() {
            try {
                if (this.isDebugMode) console.log('Loading Calendario da IndexedDB:');
                const dbRequest = indexedDB.open('simulatoreDB', 1);
                return new Promise((resolve, reject) => {
                    dbRequest.onupgradeneeded = (event) => {
                        const db = event.target.result;
                        if (!db.objectStoreNames.contains('calendari')) {
                            db.createObjectStore('calendari', { keyPath: 'id' });
                        }
                    };

                    dbRequest.onsuccess = (event) => {
                        const db = event.target.result;
                        const transaction = db.transaction('calendari', 'readonly');
                        const store = transaction.objectStore('calendari');
                        const request = store.get('cal_' + this.numero_cliente);

                        request.onsuccess = () => {
                            const compressedData = request.result?.data;
                            if (compressedData) {
                                const { decompress } = useZlib();
                                const decompressedData = decompress(compressedData);
                                resolve(decompressedData);
                            } else {
                                resolve([]);
                            }
                        };

                        request.onerror = () => {
                            reject(request.error);
                        };
                    };

                    dbRequest.onerror = () => {
                        reject(dbRequest.error);
                    };
                });
            } catch (error) {
                console.error('Errore durante il caricamento del calendario da IndexedDB:', error);
                return [];
            }
        },

        async saveCalendarioToIndexedDB(calendario) {
            try {
                const dbRequest = indexedDB.open('simulatoreDB', 1);
                return new Promise((resolve, reject) => {
                    dbRequest.onupgradeneeded = (event) => {
                        const db = event.target.result;
                        if (!db.objectStoreNames.contains('calendari')) {
                            db.createObjectStore('calendari', { keyPath: 'id' });
                        }
                    };

                    dbRequest.onsuccess = (event) => {
                        const db = event.target.result;
                        const transaction = db.transaction('calendari', 'readwrite');
                        const store = transaction.objectStore('calendari');
                        const { compress } = useZlib();
                        const compressedData = compress(calendario);
                        const request = store.put({ id: 'cal_' + this.numero_cliente, data: compressedData });

                        request.onsuccess = () => {
                            resolve(true);
                        };

                        request.onerror = () => {
                            reject(request.error);
                        };
                    };

                    dbRequest.onerror = () => {
                        reject(dbRequest.error);
                    };
                });
            } catch (error) {
                console.error('Errore durante il salvataggio del calendario in IndexedDB:', error);
            }
        },

        clearCalendario() {
            this.calendario = [];
            if (this.isDebugMode) console.log('Calendario svuotato nello store.');
        },

        calculatePrezzoListinoPerNumeroUnita() {
            if (!this.prezzo_minimo_per_sigla_ua || !this.totale_ua_per_sigla) {
                if (this.isDebugMode) {
                    console.error('prezzo_minimo_per_sigla_ua o totale_ua_per_sigla sono null o undefined.');
                }
                return;
            }

            this.prezzo_listino_per_numero_unita = {};
            let totalePrezzoListino = 0;

            const sigleuaStore = useSigleuaStore();
            if (!this.sigleUa) {
                this.sigleUa = sigleuaStore.sigleUa;
            }

            for (const sigla in this.totale_ua_per_sigla) {
                const numeroUnita = this.totale_ua_per_sigla[sigla] || 0;
                const prezzoMinimo = this.prezzo_minimo_per_sigla_ua[sigla] || 0;
                if (this.isDebugMode) {
                    console.log(`Elaborazione sigla: ${sigla}`);
                    console.log(`numeroUnita: ${numeroUnita}, prezzoMinimo: ${prezzoMinimo}`);
                }

                if (numeroUnita === 0 || prezzoMinimo === 0) {
                    if (this.isDebugMode) console.warn(`Dati mancanti o zero per sigla ${sigla}.`);
                    continue;
                }

                const siglaData = this.sigleUa.find(item => item.siglaUA === sigla);
                if (siglaData && siglaData.uaMadre) {
                    if (this.isDebugMode) console.log(`##### Sigla ${sigla} esclusa dal calcolo per uaMadre impostata.`);
                    continue;
                }

                const prezzoListino = numeroUnita * prezzoMinimo;
                this.prezzo_listino_per_numero_unita[sigla] = prezzoListino;
                totalePrezzoListino += prezzoListino;

                if (this.isDebugMode) {
                    console.log(`Prezzo Listino per ${sigla}: ${prezzoListino}`);
                }
            }

            this.totale_prezzo_listino_per_numero_unita = totalePrezzoListino;

            if (this.isDebugMode) {
                console.log('Totale Prezzo Listino per Numero Unità:', this.totale_prezzo_listino_per_numero_unita);
            }
        },

        calculatePesoSuTipologiaUaAuto() {
            const totalePrezzoListino = this.totale_prezzo_listino_per_numero_unita;

            if (totalePrezzoListino === 0) {
                if (this.isDebugMode) console.warn('Totale Prezzo Listino per Numero Unità è zero, impossibile calcolare i pesi.');
                return;
            }

            this.peso_su_tipologia_ua_auto = {};
            this.costo_su_tipologia_ua_auto = {};

            const sigleuaStore = useSigleuaStore();
            if (!this.sigleUa) {
                this.sigleUa = sigleuaStore.sigleUa;
            }
            for (const sigla in this.prezzo_listino_per_numero_unita) {
                const siglaData = this.sigleUa.find(item => item.siglaUA === sigla);
                if (siglaData && siglaData.uaMadre) {
                    if (this.isDebugMode) console.log(`##### Sigla ${sigla} esclusa dal calcolo per uaMadre impostata.`);
                    continue;
                }

                const prezzoListino = this.prezzo_listino_per_numero_unita[sigla];
                const peso = (prezzoListino * 100) / totalePrezzoListino;
                this.peso_su_tipologia_ua_auto[sigla] = Number((peso / 100).toFixed(2));
                this.costo_su_tipologia_ua_auto[sigla] = Number((this.costo_totale * this.peso_su_tipologia_ua_auto[sigla]).toFixed(2));

                if (this.isDebugMode) {
                    console.log(`Peso su Tipologia UA auto per ${sigla}: ${this.peso_su_tipologia_ua_auto[sigla]}%`);
                    console.log(`Costo su Tipologia UA auto per ${sigla}: ${this.costo_su_tipologia_ua_auto[sigla]}`);
                }
            }
         },

        handleChangeValue (key, sigla, param, value)   {
        },
        // BOOK: changePrezzoByInput cambia prezzo in base alla priorità del valore inserito
        changePrezzoByInput (key, sigla, param, value){
        },
        handleTariffaChange(key, value) {
        },
        applyPriceModifications(basePrice, garantito, modificaperc, prezzoforzato) {
            let finalPrice = basePrice;
            if (prezzoforzato && prezzoforzato > 0) {
                finalPrice = prezzoforzato;
            } else {
                if (garantito && finalPrice < garantito) {
                    finalPrice = garantito;
                }
                if (modificaperc && modificaperc !== 0) {
                    finalPrice = finalPrice * (1 + modificaperc / 100);
                }
            }
            return finalPrice;
        },

        // Funzione ausiliaria per calcolare il prezzo aggiuntivo
        calculateAdditionalPrice(n, basePrice, siglaUa) {
             
            if(n===0) return basePrice;
            if(!siglaUa || siglaUa === '' || siglaUa === null || siglaUa === undefined) {
                return basePrice;
            }
            
            // Early return se sigleUaMapBySiglaUa non contiene la siglaUa
            if(!this.sigleUaMapBySiglaUa[siglaUa]) {
                return '-';
            }
            
            const currentSiglaUa = this.sigleUaMapBySiglaUa[siglaUa];
            let persone = Number(currentSiglaUa.persone || 0);
            let personeAggiuntive = Number(currentSiglaUa.personeAggiuntive || 0);
            
            // Se non ci sono persone aggiuntive o sono meno di quelle richieste, ritorna '-'
            if(!personeAggiuntive || personeAggiuntive < n) {
                return '-';
            }
            
            // Calcolo prezzo base per persona
            const prezzoBasePerPersona = basePrice / persone;
            
            const scontoPercentuale = currentSiglaUa[`scontoLetto${n}`] || 0;  
            const scontoDecimale = scontoPercentuale / 100;
            
            // Prezzo scontato per la persona aggiuntiva n
            return prezzoBasePerPersona * (1 - scontoDecimale);
        },

        finalPriceN(n, finalPrice, siglaUa) {
            finalPrice = Number(finalPrice);
            if(n===0) return finalPrice.toFixed(0);
            let prezzoPersonaAggiuntiva = this.calculateAdditionalPrice(n, finalPrice, siglaUa);
            prezzoPersonaAggiuntiva = n * Number(prezzoPersonaAggiuntiva);
            if (prezzoPersonaAggiuntiva === '-') return '-';
            return (finalPrice + prezzoPersonaAggiuntiva).toFixed(0);
        },

        prezziRevenueN(n, prezzorevenue, siglaUa) {
            prezzorevenue = Number(prezzorevenue);
            if(n===0) return prezzorevenue;
            let isDebug = false;

            if(isDebug)  console.log('[prezziRevenueN] Parameters:', { n, prezzorevenue, siglaUa });

            const prezzoPersonaAggiuntiva = this.calculateAdditionalPrice(n, prezzorevenue, siglaUa);
            
            if (prezzoPersonaAggiuntiva === '-') return '-';
            
            const prezzoTotale = Number(prezzorevenue + prezzoPersonaAggiuntiva);

            if(isDebug) {
                console.log('[prezziRevenueN] Final calculation:', {
                    prezzoPersonaAggiuntiva,
                    prezzoTotale
                });
            }
            
            return prezzoTotale.toFixed(0);
        }
    },
});
