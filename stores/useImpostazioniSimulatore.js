// useImpostazioniSimulatore.js
import { defineStore } from 'pinia';

export const useImpostazioniSimulatore = defineStore('impostazioniSimulatore', {
    state: () => ({
        numero_cliente: '',
        gestione_costi: '',
        mol_libero: 0,
        tipo_eventi: '',
        tipo_prezzi: '',
    }),
    actions: {
        async loadImpostazioniSimulatore(numeroCliente, cache=false) {
            try {
                const response = await useApi(
                    createUrl('/resources', {
                        query: {
                            tb: 'impostazioni_simulatore',
                            pk: 'numero_cliente',
                            sk: numeroCliente,
                        },
                    }),{},cache
                );

                const dataImpostazioni = response.data.value.body;
                if (this.isDebugMode) console.log('Loaded impostazioni_simulatore data:', dataImpostazioni);
                if (dataImpostazioni) {
                    this.numero_cliente = dataImpostazioni.numero_cliente || '';
                    this.strategia_disponibilita = dataImpostazioni.strategia_disponibilita || '';
                    this.gestione_costi = dataImpostazioni.gestione_costi || '';
                    this.mol_libero = Number(dataImpostazioni.mol_libero) || 0;
                    this.tipo_eventi = dataImpostazioni.tipo_eventi || '';
                    this.tipo_prezzi = dataImpostazioni.tipo_prezzi || '';
                    if (this.isDebugMode) {
                        console.log('impostazioni_simulatore:', this.$state);
                    }
                } else {
                    this.numero_cliente = '';
                    this.gestione_costi = '';
                    this.strategia_disponibilita='';
                    this.mol_libero = 0;
                    this.tipo_eventi = '';
                    this.tipo_prezzi = '';
                    if (this.isDebugMode) console.log('No impostazioni_simulatore data found');
                }
            } catch (error) {
                console.error('Errore durante il caricamento delle impostazioni_simulatore:', error);
            }
        },
    },
    getters: {
        getImpostazioniSimulatore: (state) => ({
            numero_cliente: state.numero_cliente,
            strategia_disponibilita: state.strategia_disponibilita,
            gestione_costi: state.gestione_costi,
            mol_libero: state.mol_libero,
            tipo_eventi: state.tipo_eventi,
            tipo_prezzi: state.tipo_prezzi,
        }),
    },
});
