// useListinoPrezziStore.js
import { defineStore } from 'pinia';

export const useListinoPrezziStore = defineStore('listinoPrezzi', {
    state: () => ({
        isDebugMode: false,
        listinoPrezzi: null,
    }),
    actions: {
        async loadListinoPrezzi(versione = '000001') {
            try {
                const { data } = await useApi(
                    createUrl('/resources', {
                        query: {
                            tb: 'listino_prezzi',
                            pk: 'versione',
                            sk: versione,
                        },
                    })
                );
                const dataListino = data.value.body;
                if (dataListino) {
                    this.listinoPrezzi = dataListino;
                    if (this.isDebugMode) console.log('Loaded listinoPrezzi:', this.listinoPrezzi);
                } else {
                    this.listinoPrezzi = null;
                    if (this.isDebugMode) console.log('No listinoPrezzi data found');
                }
            } catch (error) {
                console.error('Errore durante il caricamento di listinoPrezzi:', error);
            }
        },

        trovaPrezzo(fatturatoUa) {
            if (!this.listinoPrezzi || !this.listinoPrezzi.listino) {
                console.error('Listino prezzi non caricato.');
                return null;
            }

            const listino = this.listinoPrezzi.listino;

            // Ordina l'array per sicurezza (se non è già ordinato)
            listino.sort((a, b) => a.fatturato_ua - b.fatturato_ua);

            // Trova l'elemento con fatturato_ua esattamente uguale
            const esatto = listino.find(item => item.fatturato_ua === fatturatoUa);
            if (esatto) {
                return esatto.prezzo; // Valore trovato
            }

            // Trova gli elementi immediatamente inferiori e superiori
            const inferiore = listino.findLast(item => item.fatturato_ua < fatturatoUa);
            const superiore = listino.find(item => item.fatturato_ua > fatturatoUa);

            // Se non ci sono limiti superiori o inferiori, restituisci i valori estremi
            if (!inferiore) return superiore.prezzo;
            if (!superiore) return inferiore.prezzo;

            return inferiore.prezzo;
            /*
            // Interpolazione lineare
            const peso = (fatturatoUa - inferiore.fatturato_ua) / (superiore.fatturato_ua - inferiore.fatturato_ua);
            const prezzoInterpolato = inferiore.prezzo //+ peso * (superiore.prezzo - inferiore.prezzo);

            return prezzoInterpolato;*/
        },
    },
});
