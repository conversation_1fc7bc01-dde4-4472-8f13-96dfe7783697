import { defineStore } from 'pinia';

export const useMappaSigleuaStore = defineStore('mappaSigleua', {
    state: () => ({
        isDebugMode: false,
        numero_cliente: '',
        mappa: [],
        last_update: null,
    }),
    getters: {
        sigleuaMap(state) {
            return state.mappa.reduce((map, sigla) => {
                if (sigla.sigla && sigla.mappa) {
                    map[sigla.sigla] = sigla.mappa;
                }
                return map;
            }, {});
        },
    },
    actions: {
        async loadMappaSigleua(numeroCliente, cache=false) {

            try {
                const { data } = await useApi(
                    createUrl('/resources', {
                        query: {
                            tb: 'mappa_sigleua',
                            pk: 'numero_cliente',
                            sk: numeroCliente,
                        },
                    }),
                    {},
                    cache
                );

                const dataMappa = data.value.body;
                if (dataMappa) {
                    this.numero_cliente = dataMappa.numero_cliente || '';
                    this.mappa = dataMappa.mappa || [];
                    this.last_update = dataMappa.last_update || null;
                } else {
                    this.numero_cliente = '';
                    this.mappa = [];
                    this.last_update = null;
                }
            } catch (error) {
                console.error('Errore durante il caricamento della mappa sigleua:', error);
            }
        },
        async saveMappaSigleua() {
            try {
                await $api(`/resources?tb=mappa_sigleua`, {
                    method: 'POST',
                    body: {
                        numero_cliente: this.numero_cliente,
                        mappa: this.mappa,
                    },
                });
                if (this.isDebugMode) console.log('Mappa sigleua salvata con successo.');
            } catch (error) {
                console.error('Errore durante il salvataggio della mappa sigleua:', error);
            }
        },
        addNewRecord() {
            this.mappa.push({
                mappa: '',
                sigla: '',
            });
            if (this.isDebugMode) console.log('Nuovo record aggiunto.');
        },
        deleteRecord(index) {
            this.mappa.splice(index, 1);
            if (this.isDebugMode) console.log('Record eliminato.');
        },
        markAsModified() {
            this.last_update = new Date().toISOString();
        },
        /**
         * Merge nuovi elementi nella mappa e salva il record.
         * Se `mappa` è vuoto, carica prima i dati.
         * @param {Array} newSigle - Lista di oggetti [{ chiave: valore }]
         * @param {String} numeroCliente - Numero cliente per il caricamento dei dati
         */
        async mergeNewSigle(newSigle) {


            const existingKeys = this.mappa.map(item => item.sigla); // Recupera tutte le sigle esistenti
            let hasChanges = false;

            newSigle.forEach(newEntry => {
                const [key, value] = Object.entries(newEntry)[0]; // Ottieni chiave e valore dall'oggetto
                if (!existingKeys.includes(key)) { // Verifica se la chiave non esiste già
                    this.mappa.push({
                        mappa: null,
                        sigla: key,
                    });
                    hasChanges = true;
                    if (this.isDebugMode) console.log(`Aggiunto nuovo record: sigla=${key}, mappa=${value}`);
                }
            });

            if (hasChanges) {
            this.markAsModified(); // Aggiorna last_update
                await this.saveMappaSigleua(); // Salva i cambiamenti
            } else if (this.isDebugMode) {
                console.log('Nessun cambiamento da salvare.');
            }
        },
    },
});
