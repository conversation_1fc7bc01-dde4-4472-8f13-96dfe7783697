// public/simulatore-sw.js
importScripts('https://cdn.jsdelivr.net/npm/dayjs@1.11.13/dayjs.min.js');

self.addEventListener('message', async (event) => {
    const { type, payload } = event.data;
    try {
        let result;
        if (type === 'generateCalendario') {
            result = await generateCalendario(payload);
            event.ports[0].postMessage({ type: 'generateCalendarioResult', result });
        } else if (type === 'setup') {
            result = await setup(payload);
            console.log("--- <setup> payload ",payload)
            event.ports[0].postMessage({ type: 'setupResult', result });
        }
    } catch (error) {
        event.ports[0].postMessage({ type: 'error', error: error.message });
    }
});

async function generateCalendario({
                                      numero_cliente,
                                      calendario = {},
                                      dateArray,
                                      sigle,
                                      peso_su_tipologia_ua_auto = {},
                                      costo_su_tipologia_ua_auto = {},
                                      isTraceMode = false,
                                      isDebugMode = false,
                                  }) {
    if (isDebugMode) console.log('SW - Generazione calendario per cliente:', numero_cliente);

    const calendarioBackup = {};
    for (const key in calendario) {
        if (calendario[key].tipoTariffa && calendario[key].tipoTariffa !== '-') {
            calendarioBackup[key] = { tipoTariffa: calendario[key].tipoTariffa };
        }
    }
    if (isTraceMode) console.log('SW - calendarioBackup:', calendarioBackup);

    const minDate = dayjs.min(dateArray.map(p => dayjs(p.start_date)));
    const maxDate = dayjs.max(dateArray.map(p => dayjs(p.end_date)));
    let currentDate = minDate.startOf('day');
    let openDays = 0;
    const newCalendario = {};

    while (currentDate.isSameOrBefore(maxDate, 'day')) {
        let peso = 0;
        let stato = 'C';
        const calKey = 'D_' + currentDate.format('YYYYMMDD');
        const tipoTariffa = calendarioBackup[calKey] ? calendarioBackup[calKey].tipoTariffa : '-';

        for (const periodo of dateArray) {
            const start = dayjs(periodo.start_date);
            const end = dayjs(periodo.end_date);
            if (currentDate.isBetween(start, end, 'day', '[]')) {
                peso = Number(periodo.peso_periodo) || 0;
                stato = periodo.struttura;
                break;
            }
        }
        if (stato === 'A') openDays++;

        const sigleData = {};
        sigle.forEach(sigla => {
            const statoSigla = (Math.floor(Math.random() * 100) % 2 === 0) ? 'A' : 'C';
            const costo = statoSigla === 'A'
                ? Math.floor(((peso_su_tipologia_ua_auto[sigla.siglaUA] || 0) / (openDays || 1)))
                : '-';
            sigleData[sigla.siglaUA] = {
                siglaUa: sigla.siglaUA,
                categoria: sigla.categoria,
                stato: statoSigla,
                costo,
                prezzomup: statoSigla === 'A' ? 2 : '-',
                prezzorevenue: 3,
                concorrenza: 4,
                occupazioneperc: '5%',
                profitto: 6,
                rmc: 7,
                revpar: 8,
                garantito: '',
                modificaperc: '',
                prezzoforzato: '',
                prezzo_ori: 12,
                prezzo: 12,
            };
        });

        newCalendario[calKey] = {
            data: currentDate.format('YYYY-MM-DD'),
            peso,
            openDays,
            stato,
            tipoTariffa,
            modificaPerc: '',
            sigle: Object.values(sigleData),
            sigleKeys: sigleData,
        };

        currentDate = currentDate.add(1, 'day');
    }

    if (isDebugMode) console.log('SW - Calendario generato:', newCalendario);
    return newCalendario;
}

//async function setup({ numero_cliente, useCache = false }) {
async function setup(numero_cliente) {
    let useCache = false
    console.log('SW - Esecuzione setup per cliente:', numero_cliente);

    try {
        const endpoints = [
            `/api/mappaSigleua?numero_cliente=${numero_cliente}&useCache=${useCache}`,
            `/api/cliente?numero_cliente=${numero_cliente}&useCache=${useCache}`,
            `/api/impostazioni?numero_cliente=${numero_cliente}&useCache=${useCache}`,
            `/api/eventi?numero_cliente=${numero_cliente}&useCache=${useCache}`,
            `/api/strategie?numero_cliente=${numero_cliente}&useCache=${useCache}`,
            `/api/sigle?numero_cliente=${numero_cliente}&useCache=${useCache}`,
            `/api/costi?numero_cliente=${numero_cliente}&useCache=${useCache}`,
            `/api/dateStruttura?numero_cliente=${numero_cliente}&useCache=${useCache}`,
        ];

        const results = await Promise.all(endpoints.map(url => fetch(url).then(r => {
            console.log('url ' , url ,r)
            return r.json()
        })));

        console.log('------',results)


        const [mappaSigleua, cliente, impostazioni, eventi, strategie, sigle, costi, dateStruttura] = results;
        console.log('SW - Setup completato per cliente:', numero_cliente);
        return {
            data: { mappaSigleua, cliente, impostazioni, eventi, strategie, sigle, costi, dateStruttura },
            success: true,
        };
    } catch (error) {
        console.error('SW - Errore in setup:', error);
        return { success: false, error: error.message };
    }
}
